"""
接口测试脚本
测试所有接口类的主要功能，确保接口的可用性和稳定性
"""

import sys
import traceback
from datetime import datetime


def test_jiuyan_api():
    """测试韭研数据接口"""
    print("=== 测试韭研数据接口 ===")
    try:
        from 韭研数据接口 import JiuYanDataAPI
        
        jiuyan_api = JiuYanDataAPI()
        
        # 测试获取新闻数据
        print("1. 测试获取新闻数据...")
        news_data = jiuyan_api.get_news_data(start=1, limit=5)
        if news_data:
            print(f"   ✓ 成功获取新闻数据，包含 {len(news_data.get('data', {}).get('result', []))} 条记录")
        else:
            print("   ✗ 获取新闻数据失败")
        
        # 测试搜索新闻
        print("2. 测试搜索新闻...")
        search_results = jiuyan_api.search_news_by_keyword("涨停", max_pages=1)
        print(f"   ✓ 搜索到 {len(search_results)} 条相关新闻")
        
        print("韭研数据接口测试完成\n")
        return True
        
    except Exception as e:
        print(f"   ✗ 韭研数据接口测试失败: {e}")
        traceback.print_exc()
        return False


def test_kaipaila_api():
    """测试开盘啦数据接口"""
    print("=== 测试开盘啦数据接口 ===")
    try:
        from 开盘啦数据接口 import KaiPanLaDataAPI
        
        kpl_api = KaiPanLaDataAPI()
        
        # 测试获取精选股票数据
        print("1. 测试获取精选股票数据...")
        selected_stocks = kpl_api.get_selected_stocks()
        if selected_stocks:
            print(f"   ✓ 成功获取 {len(selected_stocks)} 条精选股票数据")
        else:
            print("   ✗ 获取精选股票数据失败")
        
        # 测试获取竞价数据
        print("2. 测试获取竞价数据...")
        auction_data = kpl_api.get_auction_data()
        if auction_data:
            print(f"   ✓ 成功获取 {len(auction_data)} 条竞价数据")
        else:
            print("   ✗ 获取竞价数据失败")
        
        # 测试获取涨停原因
        print("3. 测试获取涨停原因...")
        limit_reason = kpl_api.get_limit_up_reason("300262")
        if limit_reason:
            print("   ✓ 成功获取涨停原因数据")
        else:
            print("   ✗ 获取涨停原因失败")
        
        print("开盘啦数据接口测试完成\n")
        return True
        
    except Exception as e:
        print(f"   ✗ 开盘啦数据接口测试失败: {e}")
        traceback.print_exc()
        return False


def test_wencai_api():
    """测试同花顺问财接口"""
    print("=== 测试同花顺问财接口 ===")
    try:
        from 同花顺问财接口 import TongHuaShunWenCaiAPI
        
        wencai_api = TongHuaShunWenCaiAPI()
        
        # 测试自定义查询
        print("1. 测试自定义查询...")
        query = "市值大于100亿，非st"
        custom_data = wencai_api.query_data(query)
        if custom_data is not None:
            print(f"   ✓ 自定义查询成功，获取到 {len(custom_data)} 条数据")
        else:
            print("   ✗ 自定义查询失败")
        
        # 测试查询涨停股票
        print("2. 测试查询涨停股票...")
        limit_up_data = wencai_api.query_limit_up_stocks()
        if limit_up_data is not None:
            print(f"   ✓ 查询涨停股票成功，获取到 {len(limit_up_data)} 条数据")
        else:
            print("   ✗ 查询涨停股票失败")
        
        print("同花顺问财接口测试完成\n")
        return True
        
    except Exception as e:
        print(f"   ✗ 同花顺问财接口测试失败: {e}")
        traceback.print_exc()
        return False


def test_taoguba_api():
    """测试淘股吧数据接口"""
    print("=== 测试淘股吧数据接口 ===")
    try:
        from 淘股吧数据接口 import TaoGuBaDataAPI
        
        tgb_api = TaoGuBaDataAPI()
        
        # 测试初始化
        print("1. 测试接口初始化...")
        print("   ✓ 淘股吧数据接口初始化成功")
        
        # 测试从文件读取链接
        print("2. 测试从文件读取链接...")
        links = tgb_api.get_article_links_from_file()
        print(f"   ✓ 从文件读取到 {len(links)} 个链接")
        
        # 清理资源
        tgb_api.close()
        
        print("淘股吧数据接口测试完成\n")
        return True
        
    except Exception as e:
        print(f"   ✗ 淘股吧数据接口测试失败: {e}")
        traceback.print_exc()
        return False


def test_tongdaxin_api():
    """测试通达信联动接口"""
    print("=== 测试通达信联动接口 ===")
    try:
        from 通达信联动接口 import TongDaXinLinkAPI
        
        tdx_api = TongDaXinLinkAPI()
        
        # 测试文本提取功能
        print("1. 测试文本提取功能...")
        test_text = "股票代码：000001 平安银行"
        stock_code = tdx_api.extract_six_digits(test_text)
        if stock_code:
            print(f"   ✓ 成功提取股票代码: {stock_code}")
        else:
            print("   ✗ 提取股票代码失败")
        
        # 测试发送股票代码（需要同花顺打开）
        print("2. 测试发送股票代码到同花顺...")
        success = tdx_api.send_stock_code_to_ths("000001")
        if success:
            print("   ✓ 股票代码发送成功")
        else:
            print("   ⚠ 股票代码发送失败（可能是同花顺未打开）")
        
        print("通达信联动接口测试完成\n")
        return True
        
    except Exception as e:
        print(f"   ✗ 通达信联动接口测试失败: {e}")
        traceback.print_exc()
        return False


def test_integration_api():
    """测试集成接口"""
    print("=== 测试量化数据接口集成 ===")
    try:
        from 量化数据接口集成 import QuantDataIntegration
        
        quant_api = QuantDataIntegration()
        
        # 测试获取市场概览
        print("1. 测试获取市场概览...")
        market_overview = quant_api.get_market_overview()
        if market_overview:
            print(f"   ✓ 成功获取市场概览数据，包含 {len(market_overview)} 个指标")
            for key, value in market_overview.items():
                print(f"      {key}: {value}")
        else:
            print("   ✗ 获取市场概览失败")
        
        # 测试搜索新闻
        print("2. 测试搜索新闻...")
        news_results = quant_api.search_news("涨停", source="jiuyan", max_pages=1)
        print(f"   ✓ 搜索到 {len(news_results)} 条相关新闻")
        
        # 测试获取股票数据
        print("3. 测试获取股票数据...")
        selected_stocks = quant_api.get_stock_data("selected", source="kaipaila")
        if selected_stocks:
            print(f"   ✓ 获取到 {len(selected_stocks)} 条精选股票数据")
        else:
            print("   ✗ 获取精选股票数据失败")
        
        # 清理资源
        quant_api.close_all_connections()
        
        print("量化数据接口集成测试完成\n")
        return True
        
    except Exception as e:
        print(f"   ✗ 量化数据接口集成测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始接口可用性测试...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    test_results = []
    
    # 测试各个接口
    test_results.append(("韭研数据接口", test_jiuyan_api()))
    test_results.append(("开盘啦数据接口", test_kaipaila_api()))
    test_results.append(("同花顺问财接口", test_wencai_api()))
    test_results.append(("淘股吧数据接口", test_taoguba_api()))
    test_results.append(("通达信联动接口", test_tongdaxin_api()))
    test_results.append(("量化数据接口集成", test_integration_api()))
    
    # 输出测试结果汇总
    print("=" * 60)
    print("测试结果汇总:")
    
    passed_count = 0
    for interface_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {interface_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总计: {passed_count}/{len(test_results)} 个接口测试通过")
    
    if passed_count == len(test_results):
        print("🎉 所有接口测试通过！")
    else:
        print("⚠️  部分接口测试失败，请检查相关配置和依赖")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
