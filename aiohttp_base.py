import asyncio
import aiohttp
import aiofiles
import json
import time
import random
from typing import List, Dict, Any, Optional, Union, Callable
from urllib.parse import urljoin, urlparse
import logging
from dataclasses import dataclass
from pathlib import Path


@dataclass
class RequestConfig:
    """请求配置类"""
    url: str
    method: str = 'GET'
    headers: Optional[Dict[str, str]] = None
    params: Optional[Dict[str, Any]] = None
    data: Optional[Union[Dict, str, bytes]] = None
    json_data: Optional[Dict[str, Any]] = None
    timeout: int = 30
    retry_times: int = 3
    retry_delay: float = 1.0


class AsyncSpiderBase:
    """
    异步爬虫基础类
    提供常用的HTTP请求方法和批量爬取功能
    """
    
    def __init__(self, 
                 max_concurrent: int = 10,
                 delay_range: tuple = (1, 3),
                 timeout: int = 30,
                 retry_times: int = 3,
                 user_agent: str = None):
        """
        初始化爬虫基础类
        
        Args:
            max_concurrent (int): 最大并发数，默认10
            delay_range (tuple): 请求延迟范围(秒)，默认(1, 3)
            timeout (int): 请求超时时间，默认30秒
            retry_times (int): 重试次数，默认3次
            user_agent (str): 用户代理，默认为None
        """
        self.max_concurrent = max_concurrent
        self.delay_range = delay_range
        self.timeout = timeout
        self.retry_times = retry_times
        self.session = None
        self.semaphore = asyncio.Semaphore(max_concurrent)
        
        # 默认请求头
        self.default_headers = {
            'User-Agent': user_agent or 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close_session()
    
    async def start_session(self):
        """启动HTTP会话"""
        if self.session is None:
            connector = aiohttp.TCPConnector(
                limit=100,  # 连接池大小
                limit_per_host=30,  # 每个主机的连接数
                ttl_dns_cache=300,  # DNS缓存时间
                use_dns_cache=True,
            )
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers=self.default_headers
            )
            self.logger.info("HTTP会话已启动")
    
    async def close_session(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()
            self.session = None
            self.logger.info("HTTP会话已关闭")
    
    async def _make_request(self, config: RequestConfig) -> Optional[aiohttp.ClientResponse]:
        """
        执行HTTP请求
        
        Args:
            config (RequestConfig): 请求配置
            
        Returns:
            aiohttp.ClientResponse: 响应对象，失败返回None
        """
        if not self.session:
            await self.start_session()
        
        # 合并请求头
        headers = self.default_headers.copy()
        if config.headers:
            headers.update(config.headers)
        
        for attempt in range(config.retry_times + 1):
            try:
                async with self.semaphore:  # 控制并发数
                    # 添加随机延迟
                    if attempt > 0:
                        delay = random.uniform(*self.delay_range)
                        await asyncio.sleep(delay)
                    
                    # 构建请求参数
                    kwargs = {
                        'headers': headers,
                        'params': config.params,
                        'timeout': aiohttp.ClientTimeout(total=config.timeout)
                    }
                    
                    # 根据请求方法添加数据
                    if config.method.upper() in ['POST', 'PUT', 'PATCH']:
                        if config.json_data:
                            kwargs['json'] = config.json_data
                        elif config.data:
                            kwargs['data'] = config.data
                    
                    # 发送请求
                    async with self.session.request(
                        config.method.upper(),
                        config.url,
                        **kwargs
                    ) as response:
                        self.logger.info(f"请求成功: {config.method} {config.url} - 状态码: {response.status}")
                        return response
                        
            except asyncio.TimeoutError:
                self.logger.warning(f"请求超时 (尝试 {attempt + 1}/{config.retry_times + 1}): {config.url}")
            except aiohttp.ClientError as e:
                self.logger.warning(f"请求失败 (尝试 {attempt + 1}/{config.retry_times + 1}): {config.url} - {e}")
            except Exception as e:
                self.logger.error(f"请求异常 (尝试 {attempt + 1}/{config.retry_times + 1}): {config.url} - {e}")
            
            if attempt < config.retry_times:
                retry_delay = config.retry_delay * (2 ** attempt)  # 指数退避
                await asyncio.sleep(retry_delay)
        
        self.logger.error(f"请求最终失败: {config.url}")
        return None
    
    async def get(self, url: str, **kwargs) -> Optional[str]:
        """
        异步GET请求
        
        Args:
            url (str): 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            str: 响应文本，失败返回None
        """
        config = RequestConfig(url=url, method='GET', **kwargs)
        response = await self._make_request(config)
        
        if response:
            try:
                text = await response.text()
                return text
            except Exception as e:
                self.logger.error(f"读取响应文本失败: {url} - {e}")
        
        return None
    
    async def post(self, url: str, **kwargs) -> Optional[str]:
        """
        异步POST请求
        
        Args:
            url (str): 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            str: 响应文本，失败返回None
        """
        config = RequestConfig(url=url, method='POST', **kwargs)
        response = await self._make_request(config)
        
        if response:
            try:
                text = await response.text()
                return text
            except Exception as e:
                self.logger.error(f"读取响应文本失败: {url} - {e}")
        
        return None
    
    async def get_json(self, url: str, **kwargs) -> Optional[Dict]:
        """
        异步GET请求并返回JSON
        
        Args:
            url (str): 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            dict: JSON数据，失败返回None
        """
        config = RequestConfig(url=url, method='GET', **kwargs)
        response = await self._make_request(config)
        
        if response:
            try:
                json_data = await response.json()
                return json_data
            except Exception as e:
                self.logger.error(f"解析JSON失败: {url} - {e}")
        
        return None
    
    async def post_json(self, url: str, **kwargs) -> Optional[Dict]:
        """
        异步POST请求并返回JSON
        
        Args:
            url (str): 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            dict: JSON数据，失败返回None
        """
        config = RequestConfig(url=url, method='POST', **kwargs)
        response = await self._make_request(config)
        
        if response:
            try:
                json_data = await response.json()
                return json_data
            except Exception as e:
                self.logger.error(f"解析JSON失败: {url} - {e}")
        
        return None
    
    async def download_file(self, url: str, file_path: str, **kwargs) -> bool:
        """
        异步下载文件
        
        Args:
            url (str): 文件URL
            file_path (str): 保存路径
            **kwargs: 其他请求参数
            
        Returns:
            bool: 下载是否成功
        """
        config = RequestConfig(url=url, method='GET', **kwargs)
        response = await self._make_request(config)
        
        if response:
            try:
                # 确保目录存在
                Path(file_path).parent.mkdir(parents=True, exist_ok=True)
                
                async with aiofiles.open(file_path, 'wb') as f:
                    async for chunk in response.content.iter_chunked(8192):
                        await f.write(chunk)
                
                self.logger.info(f"文件下载成功: {url} -> {file_path}")
                return True
            except Exception as e:
                self.logger.error(f"文件下载失败: {url} - {e}")
        
        return False

    async def batch_get(self, urls: List[str], **kwargs) -> List[Optional[str]]:
        """
        批量异步GET请求

        Args:
            urls (List[str]): URL列表
            **kwargs: 其他请求参数

        Returns:
            List[Optional[str]]: 响应文本列表
        """
        tasks = []
        for url in urls:
            task = self.get(url, **kwargs)
            tasks.append(task)

        self.logger.info(f"开始批量GET请求，共 {len(urls)} 个URL")
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"批量请求异常: {urls[i]} - {result}")
                processed_results.append(None)
            else:
                processed_results.append(result)

        success_count = sum(1 for r in processed_results if r is not None)
        self.logger.info(f"批量GET请求完成，成功: {success_count}/{len(urls)}")

        return processed_results

    async def batch_post(self, requests: List[Dict[str, Any]], **kwargs) -> List[Optional[str]]:
        """
        批量异步POST请求

        Args:
            requests (List[Dict]): 请求配置列表，每个字典包含url和其他参数
            **kwargs: 其他请求参数

        Returns:
            List[Optional[str]]: 响应文本列表
        """
        tasks = []
        for req in requests:
            url = req.pop('url')
            merged_kwargs = {**kwargs, **req}
            task = self.post(url, **merged_kwargs)
            tasks.append(task)

        self.logger.info(f"开始批量POST请求，共 {len(requests)} 个请求")
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"批量POST请求异常: {requests[i]} - {result}")
                processed_results.append(None)
            else:
                processed_results.append(result)

        success_count = sum(1 for r in processed_results if r is not None)
        self.logger.info(f"批量POST请求完成，成功: {success_count}/{len(requests)}")

        return processed_results

    async def batch_download(self, download_tasks: List[Dict[str, str]], **kwargs) -> List[bool]:
        """
        批量异步下载文件

        Args:
            download_tasks (List[Dict]): 下载任务列表，每个字典包含url和file_path
            **kwargs: 其他请求参数

        Returns:
            List[bool]: 下载结果列表
        """
        tasks = []
        for task_info in download_tasks:
            url = task_info['url']
            file_path = task_info['file_path']
            task = self.download_file(url, file_path, **kwargs)
            tasks.append(task)

        self.logger.info(f"开始批量下载，共 {len(download_tasks)} 个文件")
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"批量下载异常: {download_tasks[i]} - {result}")
                processed_results.append(False)
            else:
                processed_results.append(result)

        success_count = sum(1 for r in processed_results if r)
        self.logger.info(f"批量下载完成，成功: {success_count}/{len(download_tasks)}")

        return processed_results

    async def crawl_with_callback(self,
                                  urls: List[str],
                                  callback: Callable[[str, Optional[str]], Any],
                                  **kwargs) -> List[Any]:
        """
        带回调函数的批量爬取

        Args:
            urls (List[str]): URL列表
            callback (Callable): 回调函数，接收(url, response_text)参数
            **kwargs: 其他请求参数

        Returns:
            List[Any]: 回调函数返回值列表
        """
        async def process_url(url):
            response_text = await self.get(url, **kwargs)
            try:
                result = callback(url, response_text)
                # 如果回调函数返回协程，则等待它
                if asyncio.iscoroutine(result):
                    result = await result
                return result
            except Exception as e:
                self.logger.error(f"回调函数执行失败: {url} - {e}")
                return None

        tasks = [process_url(url) for url in urls]

        self.logger.info(f"开始带回调的批量爬取，共 {len(urls)} 个URL")
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"爬取异常: {urls[i]} - {result}")
                processed_results.append(None)
            else:
                processed_results.append(result)

        success_count = sum(1 for r in processed_results if r is not None)
        self.logger.info(f"带回调的批量爬取完成，成功: {success_count}/{len(urls)}")

        return processed_results

    async def save_to_file(self, data: Any, file_path: str, mode: str = 'w', encoding: str = 'utf-8'):
        """
        异步保存数据到文件

        Args:
            data (Any): 要保存的数据
            file_path (str): 文件路径
            mode (str): 文件打开模式，默认'w'
            encoding (str): 文件编码，默认'utf-8'
        """
        try:
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)

            async with aiofiles.open(file_path, mode, encoding=encoding) as f:
                if isinstance(data, (dict, list)):
                    await f.write(json.dumps(data, ensure_ascii=False, indent=2))
                else:
                    await f.write(str(data))

            self.logger.info(f"数据保存成功: {file_path}")
        except Exception as e:
            self.logger.error(f"数据保存失败: {file_path} - {e}")

    def set_proxy(self, proxy: str):
        """
        设置代理

        Args:
            proxy (str): 代理地址，格式如 'http://127.0.0.1:8080'
        """
        if self.session:
            self.logger.warning("会话已启动，代理设置将在下次启动时生效")

        # 更新默认连接器配置（需要重新启动会话）
        self._proxy = proxy
        self.logger.info(f"代理已设置: {proxy}")

    def add_header(self, key: str, value: str):
        """
        添加默认请求头

        Args:
            key (str): 请求头键
            value (str): 请求头值
        """
        self.default_headers[key] = value
        self.logger.info(f"请求头已添加: {key} = {value}")

    def remove_header(self, key: str):
        """
        移除默认请求头

        Args:
            key (str): 请求头键
        """
        if key in self.default_headers:
            del self.default_headers[key]
            self.logger.info(f"请求头已移除: {key}")

    async def get_response_info(self, url: str, **kwargs) -> Optional[Dict[str, Any]]:
        """
        获取响应详细信息

        Args:
            url (str): 请求URL
            **kwargs: 其他请求参数

        Returns:
            dict: 响应信息字典
        """
        config = RequestConfig(url=url, method='GET', **kwargs)
        response = await self._make_request(config)

        if response:
            try:
                text = await response.text()
                return {
                    'url': str(response.url),
                    'status': response.status,
                    'headers': dict(response.headers),
                    'text': text,
                    'content_type': response.content_type,
                    'charset': response.charset,
                    'content_length': response.content_length
                }
            except Exception as e:
                self.logger.error(f"获取响应信息失败: {url} - {e}")

        return None

