# 量化系统接口整理

本项目将不同数据源的接口整理成独立的类，方便快速移植使用。每个数据源都有对应的接口类，保证接口的可用性和稳定性。

## 项目结构

```
├── 韭研数据接口.py          # 韭研数据接口类
├── 开盘啦数据接口.py        # 开盘啦数据接口类
├── 同花顺问财接口.py        # 同花顺问财接口类
├── 淘股吧数据接口.py        # 淘股吧数据接口类
├── 通达信联动接口.py        # 通达信联动接口类
├── 量化数据接口集成.py      # 集成所有接口的主类
└── README.md               # 使用说明文档
```

## 接口类说明

### 1. 韭研数据接口 (JiuYanDataAPI)

提供韭研新闻检索、数据下载等功能。

**主要功能：**
- 获取韭研新闻数据
- 根据关键词搜索新闻
- 保存新闻数据到CSV文件
- 下载韭研异动解析数据

**使用示例：**
```python
from 韭研数据接口 import JiuYanDataAPI

# 创建接口实例
jiuyan_api = JiuYanDataAPI()

# 搜索包含特定关键词的新闻
results = jiuyan_api.search_news_by_keyword("涨停")

# 保存今日新闻到CSV
csv_file = jiuyan_api.save_news_to_csv()
```

### 2. 开盘啦数据接口 (KaiPanLaDataAPI)

提供开盘啦APP的各种股票数据接口功能。

**主要功能：**
- 获取精选股票数据
- 获取涨停原因
- 获取竞价数据
- 获取龙虎榜数据
- 获取市场情绪数据
- 获取板块强度数据
- 获取大单数据
- 获取人气榜数据

**使用示例：**
```python
from 开盘啦数据接口 import KaiPanLaDataAPI

# 创建接口实例
kpl_api = KaiPanLaDataAPI()

# 获取精选股票数据
selected_stocks = kpl_api.get_selected_stocks()

# 获取涨停原因
limit_reason = kpl_api.get_limit_up_reason("300262")

# 获取竞价数据
auction_data = kpl_api.get_auction_data()
```

### 3. 同花顺问财接口 (TongHuaShunWenCaiAPI)

提供同花顺问财查询功能。

**主要功能：**
- 执行问财查询
- 查询竞价数据
- 查询涨停股票
- 查询创新高股票
- 查询量比异常股票
- 查询换手率异常股票
- 分析行业分布
- 保存数据到Excel

**使用示例：**
```python
from 同花顺问财接口 import TongHuaShunWenCaiAPI

# 创建接口实例
wencai_api = TongHuaShunWenCaiAPI()

# 查询涨停股票
limit_up_data = wencai_api.query_limit_up_stocks()

# 查询创新高股票并分析行业分布
new_high_data = wencai_api.query_new_high_stocks()
industry_stats = wencai_api.analyze_industry_distribution(new_high_data)

# 自定义查询
custom_data = wencai_api.query_data("市盈率小于30，市净率小于3，非st")
```

### 4. 淘股吧数据接口 (TaoGuBaDataAPI)

提供淘股吧文章爬取功能。

**主要功能：**
- 登录淘股吧
- 提取文章内容
- 保存文章到HTML文件
- 批量爬取文章

**使用示例：**
```python
from 淘股吧数据接口 import TaoGuBaDataAPI

# 创建接口实例
tgb_api = TaoGuBaDataAPI()

# 登录并爬取单篇文章
if tgb_api.login("https://www.taoguba.com.cn/Article/3742583/1"):
    article_data = tgb_api.extract_article_content(url)
    filepath = tgb_api.save_article_to_html(article_data)

# 批量爬取文章
processed_articles = tgb_api.batch_crawl_articles()
```

### 5. 通达信联动接口 (TongDaXinLinkAPI)

提供OCR识别和股票代码发送到同花顺的功能。

**主要功能：**
- 发送股票代码到同花顺
- OCR识别文本中的股票代码
- 截取窗口指定区域
- 启动鼠标钩子监听

**使用示例：**
```python
from 通达信联动接口 import TongDaXinLinkAPI

# 创建接口实例
tdx_api = TongDaXinLinkAPI()

# 直接发送股票代码到同花顺
success = tdx_api.send_stock_code_to_ths("000001")

# 启动鼠标钩子进行实时监听
tdx_api.start_mouse_hook()
```

### 6. 量化数据接口集成 (QuantDataIntegration)

整合所有数据源的接口，提供统一的访问入口。

**主要功能：**
- 统一的数据获取接口
- 统一的数据保存接口
- 市场概览数据获取
- 多数据源支持

**使用示例：**
```python
from 量化数据接口集成 import QuantDataIntegration

# 创建集成接口实例
quant_api = QuantDataIntegration()

# 获取市场概览
market_overview = quant_api.get_market_overview()

# 搜索新闻
news_results = quant_api.search_news("涨停", source="jiuyan")

# 获取股票数据
selected_stocks = quant_api.get_stock_data("selected", source="kaipaila")
limit_up_data = quant_api.get_stock_data("limit_up", source="wencai")

# 保存数据到文件
excel_file = quant_api.save_data_to_file(selected_stocks, "股票数据", "excel")
```

## 依赖库

请确保安装以下Python库：

```bash
pip install requests pandas selenium beautifulsoup4 openpyxl pywencai
pip install pywin32 pillow pytesseract pywinhook psutil
```

## 注意事项

1. **Tesseract OCR**: 使用通达信联动接口需要安装Tesseract OCR程序
2. **Chrome驱动**: 淘股吧爬取功能需要Chrome浏览器和对应版本的ChromeDriver
3. **同花顺软件**: 通达信联动功能需要同花顺软件已经打开
4. **网络连接**: 所有接口都需要稳定的网络连接
5. **登录信息**: 部分接口需要提供登录用户名和密码

## 快速开始

1. 下载所有接口文件到同一目录
2. 安装必要的依赖库
3. 根据需要修改登录信息和配置参数
4. 运行对应的接口文件或集成文件

```python
# 快速测试所有接口
from 量化数据接口集成 import QuantDataIntegration

quant_api = QuantDataIntegration()
market_overview = quant_api.get_market_overview()
print(market_overview)
```

## 技术支持

如有问题或建议，请查看代码中的详细注释或联系开发者。

## 更新日志

- v1.0: 初始版本，包含所有基础接口功能
- 所有接口均经过测试，确保可用性和稳定性
