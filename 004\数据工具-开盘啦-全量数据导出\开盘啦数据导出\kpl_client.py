import os
from datetime import datetime
from typing import List, Dict, Any, Optional

import requests
from openpyxl import Workbook


class KPLClient:
    """
    开盘啦（KPL）数据源客户端

    设计目标：
    - 将一个数据源（开盘啦）的所有接口统一收敛到单一类，便于复用与快速移植；
    - 复用并保持现有 utils.py 的行为与数据处理逻辑，确保可用性；
    - 所有注释使用中文，便于阅读与维护。
    """

    # URL 模板映射（从 utils.py 挪入，保持一致）
    _URL_TEMPLATES: Dict[str, str] = {
        '曾跌停': 'https://apphis.longhuvip.com/w1/api/index.php?st=60&Is_st=1&FilterGem=0&Index=0&PidType=5&FilterMotherboard=0&Order=1&PhoneOSNew=1&FilterTIB=0&a=HisDaBanList&apiv=w33&Type=6&Filter=0&Day={}&c=HisHomeDingPan&VerSion=********&DeviceID=00000000-4bd0-7c8d-0000-00006d0b2bf7&',
        '跌停':   'https://apphis.longhuvip.com/w1/api/index.php?st=60&Is_st=1&FilterGem=0&Index=0&PidType=3&FilterMotherboard=0&Order=1&PhoneOSNew=1&FilterTIB=0&a=HisDaBanList&apiv=w33&Type=4&Filter=0&Day={}&c=HisHomeDingPan&VerSion=********&DeviceID=00000000-4bd0-7c8d-0000-00006d0b2bf7&',
        '竞价':   'https://apphis.longhuvip.com/w1/api/index.php?Filter=0&FilterGem=0&FilterMotherboard=0&FilterTIB=0&Index=0&Is_st=1&Order=1&PhoneOSNew=2&PidType=8&Type=18&VerSion=*******&a=HisDaBanList&apiv=w32&c=HisHomeDingPan&st=60&Day={}',
        '炸板':   'https://apphis.longhuvip.com/w1/api/index.php?st=60&Is_st=1&FilterGem=0&Index=0&PidType=2&FilterMotherboard=0&Order=1&PhoneOSNew=1&FilterTIB=0&a=HisDaBanList&apiv=w33&Type=4&Filter=0&Day={}&c=HisHomeDingPan&VerSion=********&DeviceID=00000000-4bd0-7c8d-0000-00006d0b2bf7&',
        '涨停':   'https://apphis.longhuvip.com/w1/api/index.php?Order=1&a=HisDaBanList&st=60&c=HisHomeDingPan&PhoneOSNew=1&DeviceID=00000000-296c-20ad-0000-00003eb74e84&VerSion=********&Index=0&Is_st=1&PidType=1&apiv=w31&Type=6&FilterMotherboard=0&Filter=0&FilterTIB=0&Day={}&FilterGem=0&',
        '自然涨停': 'https://apphis.longhuvip.com/w1/api/index.php?st=60&Is_st=1&FilterGem=0&Index=0&PidType=4&FilterMotherboard=0&Order=1&PhoneOSNew=1&FilterTIB=0&a=HisDaBanList&apiv=w33&Type=6&Filter=0&Day={}&c=HisHomeDingPan&VerSion=********&DeviceID=00000000-4bd0-7c8d-0000-00006d0b2bf7&'
    }

    # 数据处理配置（从 utils.py 挪入，保持一致）
    _PROCESSING_CONFIG: Dict[str, Dict[str, Any]] = {
        '曾跌停': {
            'columns_to_delete': ['Column 3', 'Column 4', 'Column 5', 'Column 6', 'Column 9', 'Column 10',
                'Column 11', 'Column 17', 'Column 18', 'Column 19', 'Column 20', 'Column 21',
                'Column 22', 'Column 23', 'Column 24', 'Column 25', 'Column 26', 'Column 27',
                'Column 28', 'Column 29', 'Column 30', 'Column 31', 'Column 32', 'Column 33'],
            'header_replacements': {
                'Column 7': '跌停时间',
                'Column 8': '开板时间',
                'Column 13': '主力净额',
                'Column 14': '成交金额',
                'Column 15': '换手率',
                'Column 16': '实际流通'
            },
            'timestamp_columns': ['Column 7', 'Column 8'],
            'divide_columns': {
                'Column 13': 10000,
                'Column 14': 100000000,
                'Column 16': 100000000
            }
        },
        '跌停': {
            'columns_to_delete': ['Column 3', 'Column 4', 'Column 5', 'Column 6', 'Column 8', 'Column 10',
                'Column 11', 'Column 17', 'Column 18', 'Column 19', 'Column 20', 'Column 21',
                'Column 22', 'Column 23', 'Column 24', 'Column 25', 'Column 26', 'Column 27',
                'Column 28', 'Column 29', 'Column 30', 'Column 31', 'Column 32', 'Column 33'],
            'header_replacements': {
                'Column 7': '跌停时间',
                'Column 9': '封单',
                'Column 13': '主力净额',
                'Column 14': '成交金额',
                'Column 15': '换手率',
                'Column 16': '实际流通'
            },
            'timestamp_columns': ['Column 7'],
            'divide_columns': {
                'Column 9': 10000,
                'Column 13': 10000,
                'Column 14': 100000000,
                'Column 16': 100000000
            }
        },
        '竞价': {
            'columns_to_delete': [
                'Column 3', 'Column 4', 'Column 6', 'Column 7', 'Column 8', 'Column 9',
                'Column 10', 'Column 11', 'Column 13', 'Column 14', 'Column 15', 'Column 30',
                'Column 17', 'Column 18', 'Column 24', 'Column 25', 'Column 26', 'Column 27',
                'Column 28', 'Column 29', 'Column 31', 'Column 32', 'Column 33'
            ],
            'header_replacements': {
                'Column 19': '涨停委买额',
                'Column 5': '实时涨幅',
                'Column 16': '实际流通',
                'Column 20': '竞价涨幅',
                'Column 21': '竞价净额',
                'Column 22': '竞价换手',
                'Column 23': '竞价成交额'
            },
            'divide_columns': {
                'Column 19': 100000000,
                'Column 21': 10000,
                'Column 23': 10000,
                'Column 16': 100000000
            }
        },
        '炸板': {
            'columns_to_delete': [
                'Column 3', 'Column 4', 'Column 6', 'Column 9', 'Column 11',
                'Column 17', 'Column 18', 'Column 19', 'Column 20', 'Column 21',
                'Column 22', 'Column 23', 'Column 24', 'Column 25', 'Column 26',
                'Column 27', 'Column 28', 'Column 29', 'Column 31', 'Column 32',
                'Column 30', 'Column 33'
            ],
            'header_replacements': {
                'Column 7': '涨停时间',
                'Column 8': '开板时间',
                'Column 13': '主力净额',
                'Column 14': '成交额',
                'Column 15': '实际换手',
                'Column 16': '实际流通'
            },
            'timestamp_columns': ['Column 7', 'Column 8'],
            'divide_columns': {
                'Column 13': 100000000,
                'Column 14': 100000000,
                'Column 16': 100000000
            }
        },
        '涨停': {
            'columns_to_filter': [2, 3, 4, 5, 7, 17, 18, 19, 20, 21, 22, 24, 26, 29, 28, 30, 31, 32],
            'header_replacements': {
                'Column 7': '涨停时间',
                'Column 9': '封单',
                'Column 13': '主力净额',
                'Column 14': '成交额',
                'Column 15': '实际换手',
                'Column 16': '实际流通',
                'Column 26': '最后涨停时间',
                'Column 28': '数量',
                'Column 24': '最大封单'
            },
            'timestamp_columns': ['Column 7', 'Column 26'],
            'divide_columns': {
                'Column 9': 10000,
                'Column 13': 10000,
                'Column 14': 100000000,
                'Column 24': 10000,
                'Column 16': 100000000
            }
        },
        '自然涨停': {
            'columns_to_delete': [
                'Column 3', 'Column 4', 'Column 5', 'Column 6', 'Column 8', 'Column 24',
                'Column 25', 'Column 26', 'Column 27', 'Column 28', 'Column 29', 'Column 30',
                'Column 18', 'Column 19', 'Column 20', 'Column 21', 'Column 22', 'Column 23',
                'Column 31', 'Column 32', 'Column 33'
            ],
            'header_replacements': {
                'Column 7': '涨停时间',
                'Column 9': '封单',
                'Column 13': '主力净额',
                'Column 14': '成交金额',
                'Column 15': '实际换手',
                'Column 16': '实际流通'
            },
            'timestamp_columns': ['Column 7'],
            'divide_columns': {
                'Column 9': 10000,
                'Column 13': 10000,
                'Column 14': 100000000,
                'Column 16': 100000000
            }
        }
    }

    def __init__(self, session: Optional[requests.Session] = None) -> None:
        # 允许注入 session 便于后续扩展（代理、重试、统一 headers 等）
        self.session = session or requests.Session()

    # ----------------------------
    # 公共抓取与导出方法
    # ----------------------------
    def _fetch_all_pages(self, base_url: str, page_step: int = 60) -> List[list]:
        """分页抓取 list 字段的所有数据。

        参数:
            base_url: 不含 Index 的基础 URL（末尾允许有 &）。
            page_step: 每次翻页的步长，默认 60（与 utils.py 保持一致）。
        返回:
            聚合后的二维数组数据（服务端返回的 list）。
        """
        all_data: List[list] = []
        index = 0
        while True:
            current_url = f"{base_url}&Index={index}"
            resp = self.session.get(current_url)
            resp.raise_for_status()
            data_json = resp.json()
            if 'list' in data_json:
                page = data_json['list']
                if not page:
                    break
                all_data.extend(page)
                index += page_step
            else:
                # 不存在 list 字段，直接结束
                break
        return all_data

    def _process_and_write_excel(self, file_type: str, all_data: List[list], save_path: str) -> None:
        """根据 file_type 的规则处理数据并写入 Excel 文件。"""
        wb = Workbook()
        ws = wb.active

        if not all_data:
            wb.save(save_path)
            return

        # 涨停 使用特殊逻辑（与 utils.py 一致）
        if file_type == '涨停':
            config = self._PROCESSING_CONFIG[file_type]
            columns_to_filter = config.get('columns_to_filter', [])
            num_columns = len(all_data[0])
            headers = [f'Column {i + 1}' for i in range(num_columns) if i not in columns_to_filter]
            ws.append(headers)

            header_indices = {header: idx for idx, header in enumerate(headers)}

            # 替换表头
            for col_name, new_name in config.get('header_replacements', {}).items():
                if col_name in header_indices:
                    idx = header_indices[col_name]
                    headers[idx] = new_name
                    ws.cell(row=1, column=idx + 1).value = new_name

            # 需要转换时间的列索引（基于替换后的表头）
            conversion_time_indices = []
            for col in config.get('timestamp_columns', []):
                new_col = config['header_replacements'].get(col, col)
                try:
                    idx = headers.index(new_col)
                    conversion_time_indices.append(idx)
                except ValueError:
                    pass

            # 需要进行除法运算的列索引
            division_indices: Dict[int, float] = {}
            for col, divisor in config.get('divide_columns', {}).items():
                new_col = config['header_replacements'].get(col, col)
                try:
                    idx = headers.index(new_col)
                    division_indices[idx] = divisor
                except ValueError:
                    pass

            # 数据行写入
            for item in all_data:
                row = [item[i] for i in range(num_columns) if i not in columns_to_filter]

                # 转换时间戳到 HH:MM
                for idx in conversion_time_indices:
                    try:
                        timestamp = int(row[idx])
                        dt = datetime.fromtimestamp(timestamp)
                        row[idx] = dt.strftime('%H:%M')
                    except (ValueError, TypeError):
                        pass

                # 除法运算并保留两位小数
                for idx, divisor in division_indices.items():
                    try:
                        value = float(row[idx])
                        row[idx] = round(value / divisor, 2)
                    except (ValueError, TypeError):
                        pass

                ws.append(row)

            wb.save(save_path)
            return

        # 其他类型，沿用原逻辑
        headers = [f'Column {i+1}' for i in range(len(all_data[0]))]
        ws.append(headers)

        config = self._PROCESSING_CONFIG.get(file_type, {})
        header_indices_1based = {header: idx for idx, header in enumerate(headers, 1)}

        for item in all_data:
            # 时间戳列处理
            for col_name in config.get('timestamp_columns', []):
                if col_name in header_indices_1based:
                    col_idx = header_indices_1based[col_name]
                    try:
                        timestamp = int(item[col_idx - 1])
                        dt = datetime.fromtimestamp(timestamp)
                        item[col_idx - 1] = dt.strftime('%H:%M')
                    except (ValueError, TypeError):
                        pass

            # 除法处理
            for col_name, divisor in config.get('divide_columns', {}).items():
                if col_name in header_indices_1based:
                    col_idx = header_indices_1based[col_name]
                    try:
                        value = float(item[col_idx - 1])
                        item[col_idx - 1] = round(value / divisor, 2)
                    except (ValueError, TypeError):
                        pass

            ws.append(item)

        # 表头替换
        for col_name, new_name in config.get('header_replacements', {}).items():
            if col_name in header_indices_1based:
                col_idx = header_indices_1based[col_name]
                ws.cell(row=1, column=col_idx).value = new_name

        # 删除不需要的列（从后往前删除）
        columns_to_delete = config.get('columns_to_delete', [])
        columns_to_delete_indices = [header_indices_1based[col] for col in columns_to_delete if col in header_indices_1based]
        for col_idx in sorted(columns_to_delete_indices, reverse=True):
            ws.delete_cols(col_idx)

        wb.save(save_path)

    # ----------------------------
    # 对外方法
    # ----------------------------
    def fetch_raw(self, date_str: str, file_type: str, page_step: int = 60) -> List[list]:
        """
        拉取原始 list 数据（不做处理）。

        参数:
            date_str: 交易日期（yyyy-mm-dd）。
            file_type: 数据类型（如：曾跌停/跌停/竞价/炸板/涨停/自然涨停）。
            page_step: 翻页步长，默认 60。
        返回:
            list[list] 原始数据。
        """
        if file_type not in self._URL_TEMPLATES:
            raise ValueError(f"不支持的 file_type: {file_type}")
        base = self._URL_TEMPLATES[file_type].format(date_str)
        return self._fetch_all_pages(base, page_step=page_step)

    def export_to_excel(self, date_str: str, file_type: str, folder_path: str, page_step: int = 60) -> str:
        """
        拉取数据并导出到 Excel，返回保存的文件路径。
        """
        all_data = self.fetch_raw(date_str, file_type, page_step=page_step)
        file_name = f"{date_str}{file_type}.xlsx"
        save_path = os.path.join(folder_path, file_name)
        self._process_and_write_excel(file_type, all_data, save_path)
        return save_path

