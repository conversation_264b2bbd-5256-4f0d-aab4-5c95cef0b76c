import win32gui
import win32ui
import win32con
import win32api
from PIL import Image
import pythoncom
import pyWinhook
import os
from datetime import datetime
import time
import threading
import sys
import re
import win32process
import psutil
from ctypes import *
import pytesseract


class TongDaXinLinkAPI:
    """
    通达信联动接口类
    提供OCR识别和股票代码发送到同花顺的功能
    """
    
    def __init__(self, tesseract_path=r'F:\Tesseract-OCR\tesseract.exe'):
        """
        初始化通达信联动接口
        
        Args:
            tesseract_path (str): Tesseract OCR程序路径
        """
        # 设置 Tesseract 的路径
        pytesseract.pytesseract.tesseract_cmd = tesseract_path
        
        # 同花顺通信相关常量
        self.FAGE_READWRITE = 0x04
        self.PROCESS_ALL_ACCESS = 0x001F0FFF
        self.VIRTUAL_MEN = (0x1000 | 0x2000)
        
        # 全局变量
        self.running = True
        self.kernel32 = windll.kernel32
        self.user32 = windll.user32
        self.IsWindowVisible = self.user32.IsWindowVisible
        
        # 鼠标钩子
        self.hm = None
    
    def _get_pids(self, pname):
        """获取进程ID列表"""
        pids = []
        for proc in psutil.process_iter():
            if pname in proc.name():
                pids.append(proc.pid)
        return pids
    
    def _get_handles(self, pid):
        """获取进程的窗口句柄列表"""
        def callback(hwnd, handles):
            if win32gui.IsWindowVisible(hwnd) and win32gui.IsWindowEnabled(hwnd):
                _, found_pid = win32process.GetWindowThreadProcessId(hwnd)
                if found_pid == pid:
                    handles.append(hwnd)
                return True
        handles = []
        win32gui.EnumWindows(callback, handles)
        return handles
    
    def _get_handle(self, pname):
        """获取进程的主窗口句柄"""
        pids = self._get_pids(pname)
        for pid in pids:
            handles = self._get_handles(pid)
            for hwnd in handles:
                if self.IsWindowVisible(hwnd):
                    return hwnd
        return None
    
    def _ths_prc_hwnd(self):
        """获取同花顺进程句柄"""
        pl = psutil.pids()
        for pid in pl:
            try:
                if psutil.Process(pid).name().lower() == 'hexin.exe':
                    if isinstance(pid, int):
                        ths_process_hwnd = self.kernel32.OpenProcess(self.PROCESS_ALL_ACCESS, False, int(pid))
                        return ths_process_hwnd
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return None
    
    def _bytes_16(self, dec_num, code):
        """转换股票代码为字节格式"""
        ascii_char = chr(dec_num)
        codex = ascii_char + str(code)
        bytes_codex = codex.encode('ascii', 'ignore')
        return bytes_codex
    
    def _ths_convert_code(self, code):
        """转换股票代码格式"""
        if str(code)[0] == '6':
            dec_num = int('11', 16)
            bytes_codex = self._bytes_16(dec_num, code)
        elif str(code).startswith('11'):
            dec_num = int('13', 16)
            bytes_codex = self._bytes_16(dec_num, code)
        elif str(code).startswith('12'):
            dec_num = int('23', 16)
            bytes_codex = self._bytes_16(dec_num, code)
        else:
            dec_num = int('21', 16)
            bytes_codex = self._bytes_16(dec_num, code)
        return bytes_codex
    
    def send_stock_code_to_ths(self, stock_code):
        """
        发送股票代码到同花顺
        
        Args:
            stock_code (str): 股票代码
            
        Returns:
            bool: 发送是否成功
        """
        try:
            ths_process_hwnd = self._ths_prc_hwnd()
            if not ths_process_hwnd:
                print("未找到同花顺进程，请确保同花顺已经打开")
                return False
                
            argv_address = self.kernel32.VirtualAllocEx(ths_process_hwnd, 0, 8, self.VIRTUAL_MEN, self.FAGE_READWRITE)
            bytes_str = self._ths_convert_code(stock_code)
            self.kernel32.WriteProcessMemory(ths_process_hwnd, argv_address, bytes_str, 7, None)
            ths_handle = self._get_handle('hexin.exe')
            if not ths_handle:
                print("未找到同花顺窗口，请确保同花顺已经打开")
                return False
            win32api.SendMessage(ths_handle, int(1168), 0, argv_address)
            print(f"已发送股票代码 {stock_code} 到同花顺")
            return True
        except Exception as e:
            print(f"发送股票代码时出错: {str(e)}")
            return False
    
    def extract_six_digits(self, text):
        """
        从文本中提取6位数字
        
        Args:
            text (str): 输入文本
            
        Returns:
            str: 提取到的6位数字，如果没有找到返回None
        """
        pattern = r'\d{6}'
        matches = re.findall(pattern, text)
        return matches[0] if matches else None
    
    def recognize_text_from_image(self, image):
        """
        从图像中识别文本
        
        Args:
            image (PIL.Image): 输入图像
            
        Returns:
            str: 识别到的文本，如果失败返回None
        """
        try:
            # 使用 Tesseract 进行 OCR 识别
            text = pytesseract.image_to_string(image, lang='eng')
            print(f"识别到的完整文本: {text}")
            
            # 查找6位数字
            digits = self.extract_six_digits(text)
            if digits:
                print(f"识别到6位数字: {digits}")
                return digits
            else:
                print("未找到6位数字")
                return None
                
        except Exception as e:
            print(f"OCR识别出错: {str(e)}")
            return None
    
    def take_screenshot(self, window_handle, capture_width=160, capture_height=70):
        """
        截取窗口指定区域的截图
        
        Args:
            window_handle: 窗口句柄
            capture_width (int): 截图宽度，默认160
            capture_height (int): 截图高度，默认70
            
        Returns:
            PIL.Image: 截图图像，如果失败返回None
        """
        try:
            # 获取窗口大小
            left, top, right, bottom = win32gui.GetWindowRect(window_handle)
            window_width = right - left
            
            # 计算截图区域
            x_offset = (window_width - capture_width) // 2
            
            # 创建设备上下文
            hwndDC = win32gui.GetWindowDC(window_handle)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()
            
            # 创建位图对象
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, capture_width, capture_height)
            saveDC.SelectObject(saveBitMap)
            
            # 复制指定区域的窗口内容到位图
            saveDC.BitBlt(
                (0, 0),
                (capture_width, capture_height),
                mfcDC,
                (x_offset, 0),
                win32con.SRCCOPY
            )
            
            # 将位图转换为字节流
            bmpinfo = saveBitMap.GetInfo()
            bmpstr = saveBitMap.GetBitmapBits(True)
            
            # 创建PIL Image对象
            image = Image.frombuffer(
                'RGB',
                (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                bmpstr, 'raw', 'BGRX', 0, 1
            )
            
            # 清理GDI资源
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(window_handle, hwndDC)
            
            return image
            
        except Exception as e:
            print(f"截图过程中出现错误: {str(e)}")
            return None
    
    def process_click_and_recognize(self, window_handle, delay=1.0):
        """
        处理点击事件并进行OCR识别
        
        Args:
            window_handle: 窗口句柄
            delay (float): 延迟时间，默认1.0秒
            
        Returns:
            str: 识别到的股票代码，如果失败返回None
        """
        time.sleep(delay)  # 延迟后截图
        if self.running:
            image = self.take_screenshot(window_handle)
            if image:
                stock_code = self.recognize_text_from_image(image)
                if stock_code:
                    # 发送股票代码到同花顺
                    self.send_stock_code_to_ths(stock_code)
                    return stock_code
        return None
    
    def _on_mouse_event(self, event):
        """鼠标事件处理函数"""
        if not self.running:
            return True
            
        if event.MessageName == "mouse left down":
            window_handle = win32gui.WindowFromPoint((event.Position[0], event.Position[1]))
            window_title = win32gui.GetWindowText(window_handle)
            print(f"\n捕获到窗口: {window_title}")
            print("等待1秒后开始截图...")
            
            # 创建新线程进行截图和识别
            screenshot_thread = threading.Thread(
                target=self.process_click_and_recognize, 
                args=(window_handle,)
            )
            screenshot_thread.daemon = True
            screenshot_thread.start()
            
        return True
    
    def start_mouse_hook(self):
        """
        启动鼠标钩子监听
        
        Returns:
            bool: 启动是否成功
        """
        try:
            # 检查Tesseract是否可用
            try:
                pytesseract.get_tesseract_version()
                print("Tesseract OCR 初始化成功")
            except Exception as e:
                print(f"Tesseract OCR 初始化失败: {e}")
                print("请确保已安装Tesseract，并且路径设置正确")
                return False
                
            # 创建鼠标钩子
            self.hm = pyWinhook.HookManager()
            self.hm.MouseAll = self._on_mouse_event
            self.hm.HookMouse()
            
            print("鼠标钩子已启动，请点击要截图的窗口...")
            print("按Ctrl+C可以退出程序")
            
            # 开始消息循环
            pythoncom.PumpMessages()
            return True
            
        except KeyboardInterrupt:
            print("\n程序被用户终止")
            return False
        except Exception as e:
            print(f"启动鼠标钩子时出现错误: {str(e)}")
            return False
    
    def stop_mouse_hook(self):
        """停止鼠标钩子"""
        self.running = False
        if self.hm:
            try:
                self.hm.UnhookMouse()
                win32gui.PostQuitMessage(0)
            except:
                pass
    
    def cleanup_and_exit(self):
        """清理资源并退出"""
        self.running = False
        if self.hm:
            win32gui.PostQuitMessage(0)
        sys.exit(0)


# 使用示例
if __name__ == "__main__":
    # 创建通达信联动接口实例
    tdx_api = TongDaXinLinkAPI()

    try:
        # 示例1: 直接发送股票代码到同花顺
        print("=== 直接发送股票代码示例 ===")
        success = tdx_api.send_stock_code_to_ths("000001")
        if success:
            print("股票代码发送成功")
        else:
            print("股票代码发送失败")

        # 示例2: 从图像文件识别股票代码
        print("\n=== 从图像识别股票代码示例 ===")
        # 如果有测试图像文件
        # try:
        #     from PIL import Image
        #     test_image = Image.open("test_stock_code.png")
        #     stock_code = tdx_api.recognize_text_from_image(test_image)
        #     if stock_code:
        #         print(f"识别到股票代码: {stock_code}")
        #         tdx_api.send_stock_code_to_ths(stock_code)
        # except FileNotFoundError:
        #     print("测试图像文件不存在")

        # 示例3: 启动鼠标钩子进行实时监听
        print("\n=== 启动鼠标钩子监听示例 ===")
        print("即将启动鼠标钩子，点击任意窗口进行OCR识别...")
        print("注意：需要确保同花顺已经打开")

        # 启动鼠标钩子（这会阻塞程序直到用户按Ctrl+C）
        tdx_api.start_mouse_hook()

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {str(e)}")
    finally:
        # 清理资源
        tdx_api.cleanup_and_exit()
