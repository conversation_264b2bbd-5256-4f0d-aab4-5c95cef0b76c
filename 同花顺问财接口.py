import pywencai
import pandas as pd
from datetime import datetime, timedelta
import sys
from collections import defaultdict


class TongHuaShunWenCaiAPI:
    """
    同花顺问财数据接口类
    提供同花顺问财查询功能
    """
    
    def __init__(self):
        """初始化同花顺问财接口"""
        # 设置pandas显示选项
        pd.set_option('display.max_columns', None)
        pd.set_option('display.max_rows', None)
        pd.set_option('display.width', None)
        pd.set_option('display.max_colwidth', None)
    
    def query_data(self, query, sort_key='', sort_order='asc', loop=True):
        """
        执行问财查询
        
        Args:
            query (str): 查询语句
            sort_key (str): 排序字段，默认为空
            sort_order (str): 排序方式，'asc'或'desc'，默认'asc'
            loop (bool): 是否循环获取所有数据，默认True
            
        Returns:
            pandas.DataFrame: 查询结果数据框，如果失败返回None
        """
        try:
            print(f"正在执行问财查询: {query}")
            result = pywencai.get(query=query, sort_key=sort_key, sort_order=sort_order, loop=loop)
            print(f"查询成功，获得 {len(result)} 条数据")
            return result
        except Exception as e:
            print(f"问财查询失败: {e}")
            return None
    
    def query_auction_data(self, date=None, limit_up_ratio=6, auction_amount=True):
        """
        查询竞价数据
        
        Args:
            date (str): 查询日期，格式'YYYY-MM-DD'，默认为今天
            limit_up_ratio (int): 竞价涨幅阈值，默认6%
            auction_amount (bool): 是否包含竞价金额，默认True
            
        Returns:
            pandas.DataFrame: 竞价数据，如果失败返回None
        """
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        query_parts = [
            f"{date}",
            "竞价涨停",
            f"竞价涨幅大于{limit_up_ratio}"
        ]
        
        if auction_amount:
            query_parts.append("竞价金额+竞价未匹配金额")
        
        query_parts.append("收盘涨幅")
        
        query = "，".join(query_parts)
        
        return self.query_data(query)
    
    def query_limit_up_stocks(self, date=None, exclude_st=True):
        """
        查询涨停股票
        
        Args:
            date (str): 查询日期，格式'YYYY-MM-DD'，默认为今天
            exclude_st (bool): 是否排除ST股票，默认True
            
        Returns:
            pandas.DataFrame: 涨停股票数据，如果失败返回None
        """
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        query_parts = [f"{date}", "涨停"]
        
        if exclude_st:
            query_parts.append("非st")
        
        query = "，".join(query_parts)
        
        return self.query_data(query)
    
    def query_new_high_stocks(self, date=None, days=100, exclude_st=True, include_industry=True):
        """
        查询创新高股票
        
        Args:
            date (str): 查询日期，格式'YYYY-MM-DD'，默认为今天
            days (int): 新高天数，默认100日
            exclude_st (bool): 是否排除ST股票，默认True
            include_industry (bool): 是否包含行业信息，默认True
            
        Returns:
            pandas.DataFrame: 创新高股票数据，如果失败返回None
        """
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        query_parts = [
            f"{date}日",
            f"收盘价前复权创{days}日新高"
        ]
        
        if exclude_st:
            query_parts.append("非st")
        
        if include_industry:
            query_parts.append("行业")
        
        query = "，".join(query_parts)
        
        return self.query_data(query)
    
    def query_volume_ratio_stocks(self, date=None, volume_ratio=2, exclude_st=True):
        """
        查询量比异常股票
        
        Args:
            date (str): 查询日期，格式'YYYY-MM-DD'，默认为今天
            volume_ratio (float): 量比阈值，默认2倍
            exclude_st (bool): 是否排除ST股票，默认True
            
        Returns:
            pandas.DataFrame: 量比异常股票数据，如果失败返回None
        """
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        query_parts = [
            f"{date}",
            f"量比大于{volume_ratio}"
        ]
        
        if exclude_st:
            query_parts.append("非st")
        
        query = "，".join(query_parts)
        
        return self.query_data(query)
    
    def query_turnover_rate_stocks(self, date=None, turnover_rate=10, exclude_st=True):
        """
        查询换手率异常股票
        
        Args:
            date (str): 查询日期，格式'YYYY-MM-DD'，默认为今天
            turnover_rate (float): 换手率阈值，默认10%
            exclude_st (bool): 是否排除ST股票，默认True
            
        Returns:
            pandas.DataFrame: 换手率异常股票数据，如果失败返回None
        """
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        query_parts = [
            f"{date}",
            f"换手率大于{turnover_rate}"
        ]
        
        if exclude_st:
            query_parts.append("非st")
        
        query = "，".join(query_parts)
        
        return self.query_data(query)
    
    def query_price_change_stocks(self, date=None, min_change=5, max_change=None, exclude_st=True):
        """
        查询涨跌幅范围股票
        
        Args:
            date (str): 查询日期，格式'YYYY-MM-DD'，默认为今天
            min_change (float): 最小涨跌幅，默认5%
            max_change (float): 最大涨跌幅，默认None（不限制）
            exclude_st (bool): 是否排除ST股票，默认True
            
        Returns:
            pandas.DataFrame: 涨跌幅范围股票数据，如果失败返回None
        """
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        query_parts = [f"{date}"]
        
        if max_change is not None:
            query_parts.append(f"涨跌幅在{min_change}%到{max_change}%之间")
        else:
            query_parts.append(f"涨跌幅大于{min_change}%")
        
        if exclude_st:
            query_parts.append("非st")
        
        query = "，".join(query_parts)
        
        return self.query_data(query)
    
    def analyze_industry_distribution(self, df, industry_column=None):
        """
        分析行业分布
        
        Args:
            df (pandas.DataFrame): 包含行业信息的数据框
            industry_column (str): 行业列名，如果为None则自动查找包含'行业'的列
            
        Returns:
            dict: 行业分布统计结果
        """
        if df is None or df.empty:
            print("数据框为空，无法分析行业分布")
            return {}
        
        # 自动查找行业列
        if industry_column is None:
            for col in df.columns:
                if '行业' in col:
                    industry_column = col
                    break
        
        if industry_column is None or industry_column not in df.columns:
            print("未找到行业列，无法分析行业分布")
            return {}
        
        industry_stats = defaultdict(list)
        stock_name_column = None
        
        # 查找股票名称列
        for col in df.columns:
            if '股票简称' in col or '名称' in col:
                stock_name_column = col
                break
        
        for idx, row in df.iterrows():
            industry_full = row[industry_column]
            stock_name = row[stock_name_column] if stock_name_column else f"股票{idx}"
            
            # 提取行业大类（第一个'-'前的部分）
            if isinstance(industry_full, str) and '-' in industry_full:
                industry_main = industry_full.split('-')[0]
                industry_stats[industry_main].append(stock_name)
            elif isinstance(industry_full, str):
                industry_stats[industry_full].append(stock_name)
        
        # 按出现次数排序
        sorted_stats = dict(sorted(industry_stats.items(), key=lambda x: len(x[1]), reverse=True))
        
        print("=== 行业分布分析 ===")
        for industry, stocks in sorted_stats.items():
            print(f"{industry}: {len(stocks)}只股票 - {', '.join(stocks[:5])}")  # 只显示前5只股票
            if len(stocks) > 5:
                print(f"  ... 还有{len(stocks)-5}只股票")
        
        return sorted_stats
    
    def save_to_excel(self, df, filename=None, sheet_name='数据'):
        """
        保存数据到Excel文件
        
        Args:
            df (pandas.DataFrame): 要保存的数据框
            filename (str): 文件名，如果为None则使用当前日期
            sheet_name (str): 工作表名称，默认'数据'
            
        Returns:
            str: 保存的文件路径
        """
        if df is None or df.empty:
            print("数据框为空，无法保存")
            return None
        
        if filename is None:
            filename = f"{datetime.now().strftime('%Y%m%d')}_问财数据.xlsx"
        
        try:
            df.to_excel(filename, sheet_name=sheet_name, index=False)
            print(f"数据已保存到: {filename}")
            return filename
        except Exception as e:
            print(f"保存Excel文件失败: {e}")
            return None


# 使用示例
if __name__ == "__main__":
    # 创建同花顺问财接口实例
    wencai_api = TongHuaShunWenCaiAPI()

    # 示例1: 查询竞价数据
    print("=== 查询竞价数据示例 ===")
    auction_data = wencai_api.query_auction_data()
    if auction_data is not None:
        print(f"获取到 {len(auction_data)} 条竞价数据")
        print(auction_data.head())

    # 示例2: 查询涨停股票
    print("\n=== 查询涨停股票示例 ===")
    limit_up_data = wencai_api.query_limit_up_stocks()
    if limit_up_data is not None:
        print(f"获取到 {len(limit_up_data)} 只涨停股票")
        print(limit_up_data.head())

    # 示例3: 查询创新高股票并分析行业分布
    print("\n=== 查询创新高股票示例 ===")
    new_high_data = wencai_api.query_new_high_stocks()
    if new_high_data is not None:
        print(f"获取到 {len(new_high_data)} 只创新高股票")
        # 分析行业分布
        industry_stats = wencai_api.analyze_industry_distribution(new_high_data)

    # 示例4: 查询量比异常股票
    print("\n=== 查询量比异常股票示例 ===")
    volume_ratio_data = wencai_api.query_volume_ratio_stocks(volume_ratio=3)
    if volume_ratio_data is not None:
        print(f"获取到 {len(volume_ratio_data)} 只量比异常股票")

    # 示例5: 自定义查询
    print("\n=== 自定义查询示例 ===")
    custom_query = "2025-01-10，市盈率小于30，市净率小于3，非st"
    custom_data = wencai_api.query_data(custom_query)
    if custom_data is not None:
        print(f"自定义查询获取到 {len(custom_data)} 条数据")

    # 示例6: 保存数据到Excel
    if limit_up_data is not None:
        print("\n=== 保存数据到Excel示例 ===")
        excel_file = wencai_api.save_to_excel(limit_up_data, "涨停股票数据.xlsx")
        if excel_file:
            print(f"数据已保存到: {excel_file}")
