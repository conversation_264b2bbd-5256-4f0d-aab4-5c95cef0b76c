import requests
import json
import hashlib
import csv
import time
import os
import pandas as pd
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup
import openpyxl


class JiuYanDataAPI:
    """
    韭研数据接口类
    提供韭研新闻检索、数据下载等功能
    """
    
    def __init__(self):
        """初始化韭研数据接口"""
        self.base_url = "https://app.jiuyangongshe.com"
        self.web_url = "https://www.jiuyangongshe.com"
        self.headers = {
            "host": "app.jiuyangongshe.com",
            "sec-ch-ua-platform": "Windows",
            "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
            "sec-ch-ua-mobile": "?0",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "accept": "application/json, text/plain, */*",
            "content-type": "application/json",
            "platform": "1",
            "origin": "https://www.jiuyangongshe.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://www.jiuyangongshe.com/",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "priority": "u=1, i"
        }
    
    def _calculate_md5(self):
        """计算MD5认证信息"""
        timestamp = str(int(time.time() * 1000))
        string = 'Uu0KfOB8iUP69d3c:' + timestamp
        md5_hash = hashlib.md5()
        md5_hash.update(string.encode('utf-8'))
        md5_hex = md5_hash.hexdigest()
        return {"timestamp": timestamp, "token": md5_hex}
    
    def get_news_data(self, start=1, limit=20, category_id="", order=0, type_filter=0):
        """
        获取韭研新闻数据
        
        Args:
            start (int): 起始页码，默认为1
            limit (int): 每页数量，默认为20
            category_id (str): 分类ID，默认为空
            order (int): 排序方式，默认为0
            type_filter (int): 类型过滤，默认为0
            
        Returns:
            dict: API返回的数据，如果失败返回None
        """
        auth_data = self._calculate_md5()
        timestamp = auth_data["timestamp"]
        token = auth_data["token"]
        
        # 更新headers中的认证信息
        self.headers.update({
            "timestamp": timestamp,
            "token": token,
            "cookie": f"SESSION=Mjc5NGRhODMtOTAzZi00NmRjLWE2OTUtZjM5ZTg1NzU1MTNl; Hm_lvt_58aa18061df7855800f2a1b32d6da7f4={timestamp}; Hm_lpvt_58aa18061df7855800f2a1b32d6da7f4={timestamp}"
        })
        
        url = f"{self.base_url}/jystock-app/api/v2/article/community"
        
        payload = {
            "category_id": category_id,
            "limit": limit,
            "order": order,
            "start": start,
            "type": type_filter,
            "back_garden": 0
        }
        
        try:
            print(f"正在请求韭研新闻数据，页码: {start}...")
            response = requests.post(url, headers=self.headers, json=payload)
            response.raise_for_status()
            print(f"请求成功，状态码: {response.status_code}")
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"请求韭研新闻数据失败: {e}")
            return None
        except json.JSONDecodeError:
            print("无法解析JSON响应")
            return None
    
    def search_news_by_keyword(self, keyword, target_date=None, max_pages=10):
        """
        根据关键词搜索韭研新闻
        
        Args:
            keyword (str): 搜索关键词
            target_date (datetime.date): 目标日期，默认为今天
            max_pages (int): 最大搜索页数，默认为10
            
        Returns:
            list: 匹配的新闻列表
        """
        if target_date is None:
            target_date = datetime.now().date()
        
        results = []
        start = 1
        
        while start <= max_pages:
            data = self.get_news_data(start=start)
            if not data or 'data' not in data or 'result' not in data['data']:
                break
                
            result_list = data['data']['result']
            found_target_date = False
            
            for item in result_list:
                create_time_str = item.get('create_time', '')
                title = item.get('title', '')
                
                try:
                    create_time = datetime.strptime(create_time_str.split(' ')[0], '%Y-%m-%d').date()
                    
                    # 如果找到目标日期的数据
                    if create_time == target_date:
                        found_target_date = True
                        if keyword in title:
                            results.append({
                                'title': title,
                                'create_time': create_time_str,
                                'content': item.get('content', ''),
                                'article_id': item.get('article_id', ''),
                                'article_link': f"https://www.jiuyangongshe.com/a/{item.get('article_id', '')}",
                                'stock_list': [stock.get('name', '') for stock in item.get('stock_list', [])]
                            })
                    elif create_time < target_date:
                        # 如果日期已经小于目标日期，停止搜索
                        return results
                        
                except ValueError:
                    continue
            
            if not found_target_date and start > 1:
                break
                
            start += 1
            
        return results
    
    def save_news_to_csv(self, target_date=None, max_pages=10):
        """
        保存韭研新闻数据到CSV文件
        
        Args:
            target_date (datetime.date): 目标日期，默认为今天
            max_pages (int): 最大搜索页数，默认为10
            
        Returns:
            str: 保存的CSV文件名
        """
        if target_date is None:
            target_date = datetime.now().date()
        
        csv_filename = f'{target_date.strftime("%Y%m%d")}_articles.csv'
        existing_ids = self._get_existing_article_ids(csv_filename)
        
        # 以追加模式打开CSV文件
        with open(csv_filename, 'a', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['title', 'create_time', 'content', 'article_id', 'article_link', 'stock_list_names']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            # 如果文件为空，写入表头
            if csvfile.tell() == 0:
                writer.writeheader()
            
            new_data_count = 0
            start = 1
            
            while start <= max_pages:
                data = self.get_news_data(start=start)
                if not data or 'data' not in data or 'result' not in data['data']:
                    break
                    
                result_list = data['data']['result']
                found_target_date = False
                
                for item in reversed(result_list):
                    create_time_str = item.get('create_time', '')
                    
                    try:
                        create_time = datetime.strptime(create_time_str.split(' ')[0], '%Y-%m-%d').date()
                        
                        if create_time == target_date:
                            found_target_date = True
                            article_id = item.get('article_id', '')
                            
                            if article_id not in existing_ids:
                                writer.writerow({
                                    'title': item.get('title', ''),
                                    'create_time': create_time_str,
                                    'content': item.get('content', ''),
                                    'article_id': article_id,
                                    'article_link': f"https://www.jiuyangongshe.com/a/{article_id}",
                                    'stock_list_names': ",".join([stock.get('name', '') for stock in item.get('stock_list', [])])
                                })
                                new_data_count += 1
                        elif create_time < target_date:
                            return csv_filename
                            
                    except ValueError:
                        continue
                
                if not found_target_date and start > 1:
                    break
                    
                start += 1
            
            print(f"本次处理共写入 {new_data_count} 条新数据到 {csv_filename}")
            
        return csv_filename
    
    def _get_existing_article_ids(self, csv_filename):
        """获取CSV文件中已存在的文章ID"""
        existing_ids = set()
        try:
            with open(csv_filename, 'r', encoding='utf-8-sig') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    existing_ids.add(row['article_id'])
        except FileNotFoundError:
            pass
        return existing_ids

    def download_action_data(self, target_date=None, username="13336343861", password="eight986"):
        """
        下载韭研异动解析数据

        Args:
            target_date (datetime.date): 目标日期，默认为今天
            username (str): 登录用户名
            password (str): 登录密码

        Returns:
            str: 保存的Excel文件路径，如果失败返回None
        """
        if target_date is None:
            target_date = datetime.now().date()

        url = f'{self.web_url}/action/{target_date.strftime("%Y-%m-%d")}'

        # 设置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--no-sandbox")

        driver = None
        try:
            print("正在启动Chrome浏览器...")
            driver = webdriver.Chrome(options=chrome_options)
            driver.implicitly_wait(10)

            print(f"正在访问韭研异动页面: {url}")
            driver.get(url)

            # 等待并点击账号密码登录
            wait = WebDriverWait(driver, 10)
            account_tab = wait.until(EC.element_to_be_clickable((By.XPATH, "//div[@id='tab-accounts']")))
            account_tab.click()
            print("已切换到账号密码登录")

            time.sleep(3)

            # 手动登录提示
            print(f"请手动输入用户名: {username}")
            print(f"请手动输入密码: {password}")

            # 倒计时等待手动登录
            for i in range(15, 0, -1):
                print(f"倒计时 {i} 秒，请手动完成登录")
                time.sleep(1)

            # 点击全部异动解析标签
            try:
                driver.find_element(By.XPATH, "//div[text()='全部异动解析']").click()
                print("已点击全部异动解析标签")
            except Exception as e:
                print(f"点击全部异动解析标签失败: {e}")

            time.sleep(3)

            # 获取页面源代码
            page_source = driver.page_source

            # 保存页面源代码
            with open("page_source.html", "w", encoding="utf-8") as file:
                file.write(page_source)

            # 解析页面数据
            excel_file = self._parse_action_data(page_source, target_date)

            return excel_file

        except Exception as e:
            print(f"下载韭研异动数据失败: {e}")
            return None
        finally:
            if driver:
                driver.quit()

    def _parse_action_data(self, html_content, target_date):
        """解析韭研异动数据"""
        soup = BeautifulSoup(html_content, 'html.parser')

        data = []
        modules = soup.find_all('li', class_='module')

        for module in modules:
            module_name_element = module.find('div', class_='fs18-bold lf')
            module_number_element = module.find('div', class_='number lf')

            if module_name_element:
                module_name = module_name_element.text.strip()
                module_number = module_number_element.text.strip() if module_number_element else ""
                module_name_module_number = f"{module_name}({module_number})"

                stocks = module.find_all('div', class_='table-box')
                for stock in stocks:
                    stock_names = stock.find_all('div', class_='shrink fs15-bold')
                    stock_codes = stock.find_all('div', class_='shrink fs12-bold-ash force-wrap')
                    analyses = stock.find_all('pre', class_='pre tl hilll')

                    for i in range(len(stock_names)):
                        if i < len(stock_codes) and i < len(analyses):
                            stock_name = stock_names[i].text.strip()
                            stock_code = stock_codes[i].text.strip()
                            analysis = analyses[i].text.strip()
                            data.append([stock_code, stock_name, module_name_module_number, analysis])

        # 创建DataFrame并保存到Excel
        df = pd.DataFrame(data, columns=['股票代码', '股票名称', '板块', '原因详解'])
        excel_file = f"{target_date.strftime('%Y-%m-%d')} 韭研.xlsx"
        df.to_excel(excel_file, index=False)

        # 处理Excel文件
        self._process_excel_file(excel_file)

        print(f"韭研异动数据已保存到: {excel_file}")
        return excel_file

    def _process_excel_file(self, excel_file_path):
        """处理Excel文件，删除前缀并调整格式"""
        try:
            workbook = openpyxl.load_workbook(excel_file_path)
            sheet = workbook.active

            # 插入空白列
            sheet.insert_cols(4)

            # 删除股票代码中的前缀
            for row in sheet.iter_rows(min_row=1, max_col=sheet.max_column, values_only=False):
                cell = row[0]  # 第一列股票代码
                if cell.value and isinstance(cell.value, str):
                    cell.value = cell.value.replace("sh", "").replace("sz", "").replace("bj", "")

            # 复制第5列中"1、"前面的文本到第4列
            for row in sheet.iter_rows(min_row=1, max_row=sheet.max_row, max_col=sheet.max_column, values_only=False):
                cell_value = row[4].value  # 第5列
                if cell_value and isinstance(cell_value, str) and "1、" in cell_value:
                    text_before_marker = cell_value.split("1、")[0].strip()
                    row[3].value = text_before_marker  # 第4列

            # 删除第5列中"1、"前面的文本
            for row in sheet.iter_rows(min_row=1, max_row=sheet.max_row, max_col=sheet.max_column, values_only=False):
                cell = row[4]  # 第5列
                cell_value = cell.value
                if cell_value and isinstance(cell_value, str) and "1、" in cell_value:
                    split_index = cell_value.find("1、")
                    if split_index != -1:
                        text_after_marker = cell_value[split_index:].strip()
                        cell.value = text_after_marker

            workbook.save(excel_file_path)
            print("Excel文件处理完成")

        except Exception as e:
            print(f"处理Excel文件失败: {e}")


# 使用示例
if __name__ == "__main__":
    # 创建韭研数据接口实例
    jiuyan_api = JiuYanDataAPI()

    # 示例1: 搜索包含特定关键词的新闻
    print("=== 搜索新闻示例 ===")
    results = jiuyan_api.search_news_by_keyword("涨停")
    for result in results[:3]:  # 只显示前3条
        print(f"标题: {result['title']}")
        print(f"时间: {result['create_time']}")
        print(f"相关股票: {', '.join(result['stock_list'])}")
        print("-" * 50)

    # 示例2: 保存今日新闻到CSV
    print("\n=== 保存新闻到CSV示例 ===")
    csv_file = jiuyan_api.save_news_to_csv()
    print(f"新闻数据已保存到: {csv_file}")

    # 示例3: 下载异动数据（需要手动登录）
    # print("\n=== 下载异动数据示例 ===")
    # excel_file = jiuyan_api.download_action_data()
    # if excel_file:
    #     print(f"异动数据已保存到: {excel_file}")
