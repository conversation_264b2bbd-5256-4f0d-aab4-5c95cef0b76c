# Excel数据可视化应用

这是一个使用Flask和ECharts的数据可视化Web应用，可以将Excel数据绘制为曲线图。

## 功能

- 从`result.xlsx`文件读取数据
- 将数据绘制为曲线图，包括以下三条曲线：
  - 值为1的数据百分比（值为1的数据除以总数据条数乘以100）
  - 值为2的数据百分比（值为2的数据除以总数据条数乘以100）
  - 最大值

## 安装与运行

1. 确保已安装Python 3.7+

2. 安装所需的依赖包：

```bash
pip install -r requirements.txt
```

3. 确保`result.xlsx`文件在程序根目录下

4. 运行应用：

```bash
python app.py
```

5. 在浏览器中访问：http://127.0.0.1:5000

## 文件结构

- `app.py` - Flask应用主文件
- `requirements.txt` - 项目依赖
- `templates/index.html` - 前端页面
- `result.xlsx` - 数据文件（Excel格式）

## 注意事项

- Excel数据文件必须包含以下列：
  - `日期`
  - `值为 1 的条数`
  - `值为 2 的条数`
  - `最大值`
  - `总共的数据条数` 