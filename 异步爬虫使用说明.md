# 异步爬虫基础类使用说明

本项目提供了基于aiohttp的异步爬虫基础类，支持高并发、批量爬取、错误重试等功能。

## 文件结构

```
├── 异步爬虫基础类.py      # 通用异步爬虫基础类
├── API爬虫类.py          # API专用爬虫类
└── 异步爬虫使用说明.md    # 使用说明文档
```

## 主要特性

### 🚀 高性能异步
- 基于aiohttp实现真正的异步IO
- 支持自定义并发数控制
- 连接池复用，提高效率

### 🛡️ 稳定可靠
- 自动重试机制（指数退避）
- 完善的异常处理
- 请求超时控制

### 🔧 功能丰富
- GET、POST、PUT、DELETE等HTTP方法
- 批量请求处理
- 文件下载功能
- 代理支持
- 自定义请求头

### 📊 监控友好
- 详细的日志记录
- 请求成功率统计
- 进度跟踪

## 安装依赖

```bash
pip install aiohttp aiofiles beautifulsoup4
```

## 基础使用

### 1. 简单的GET请求

```python
import asyncio
from 异步爬虫基础类 import AsyncSpiderBase

async def simple_get():
    async with AsyncSpiderBase() as spider:
        response = await spider.get("https://httpbin.org/get")
        print(response)

asyncio.run(simple_get())
```

### 2. POST请求

```python
async def simple_post():
    async with AsyncSpiderBase() as spider:
        data = {"key": "value", "test": "data"}
        response = await spider.post("https://httpbin.org/post", json_data=data)
        print(response)

asyncio.run(simple_post())
```

### 3. 批量请求

```python
async def batch_requests():
    async with AsyncSpiderBase(max_concurrent=5) as spider:
        urls = [
            "https://httpbin.org/delay/1",
            "https://httpbin.org/delay/2",
            "https://httpbin.org/get?page=1",
            "https://httpbin.org/get?page=2"
        ]
        results = await spider.batch_get(urls)
        print(f"成功获取 {sum(1 for r in results if r)} 个响应")

asyncio.run(batch_requests())
```

## 高级功能

### 1. 自定义配置

```python
spider = AsyncSpiderBase(
    max_concurrent=10,      # 最大并发数
    delay_range=(0.5, 2.0), # 请求延迟范围
    timeout=30,             # 超时时间
    retry_times=3,          # 重试次数
    user_agent="Custom Bot" # 自定义User-Agent
)
```

### 2. 带回调函数的爬取

```python
async def crawl_with_callback():
    def process_response(url, response_text):
        if response_text:
            return {
                'url': url,
                'length': len(response_text),
                'title': extract_title(response_text)  # 自定义处理函数
            }
        return None
    
    async with AsyncSpiderBase() as spider:
        urls = ["https://example.com", "https://httpbin.org"]
        results = await spider.crawl_with_callback(urls, process_response)
        print(results)

asyncio.run(crawl_with_callback())
```

### 3. 文件下载

```python
async def download_files():
    async with AsyncSpiderBase() as spider:
        # 单个文件下载
        success = await spider.download_file(
            "https://httpbin.org/image/png",
            "downloads/image.png"
        )
        
        # 批量文件下载
        download_tasks = [
            {"url": "https://httpbin.org/image/jpeg", "file_path": "downloads/img1.jpg"},
            {"url": "https://httpbin.org/image/png", "file_path": "downloads/img2.png"}
        ]
        results = await spider.batch_download(download_tasks)
        print(f"下载成功: {sum(results)} 个文件")

asyncio.run(download_files())
```

## 网页爬取专用类

### WebScrapingSpider 使用示例

```python
from 异步爬虫基础类 import WebScrapingSpider

async def web_scraping():
    async with WebScrapingSpider() as spider:
        # 获取页面所有链接
        links = await spider.get_page_links("https://example.com")
        print(f"发现 {len(links)} 个链接")
        
        # 获取页面所有图片
        images = await spider.get_page_images("https://example.com")
        print(f"发现 {len(images)} 个图片")
        
        # 爬取网站地图
        site_map = await spider.crawl_site_map(
            "https://example.com",
            max_depth=2,
            max_pages=50
        )
        print(f"网站地图包含 {len(site_map)} 个页面")

asyncio.run(web_scraping())
```

## API爬虫专用类

### APISpider 使用示例

```python
from API爬虫类 import APISpider

async def api_crawling():
    async with APISpider(
        base_url="https://jsonplaceholder.typicode.com",
        api_key="your-api-key"  # 可选
    ) as api_spider:
        
        # 单个API调用
        user = await api_spider.api_get("/users/1")
        print(f"用户: {user.get('name')}")
        
        # 批量API调用
        requests = [
            {"method": "GET", "endpoint": "/users/1"},
            {"method": "GET", "endpoint": "/users/2"},
            {"method": "POST", "endpoint": "/posts", "data": {"title": "Test"}}
        ]
        results = await api_spider.batch_api_calls(requests)
        
        # 分页API调用
        all_posts = await api_spider.paginated_api_call(
            "/posts",
            page_size=10,
            max_pages=5
        )
        print(f"获取到 {len(all_posts)} 条数据")

asyncio.run(api_crawling())
```

## 实际应用示例

### 1. 爬取股票数据

```python
async def crawl_stock_data():
    async with AsyncSpiderBase(max_concurrent=5) as spider:
        # 构建股票代码列表
        stock_codes = ["000001", "000002", "600000", "600036"]
        
        # 批量获取股票数据
        urls = [f"https://api.example.com/stock/{code}" for code in stock_codes]
        results = await spider.batch_get(urls)
        
        # 处理结果
        stock_data = []
        for i, result in enumerate(results):
            if result:
                data = json.loads(result)
                stock_data.append({
                    'code': stock_codes[i],
                    'price': data.get('price'),
                    'change': data.get('change')
                })
        
        # 保存数据
        await spider.save_to_file(stock_data, "stock_data.json")
        print(f"成功获取 {len(stock_data)} 只股票数据")
```

### 2. 新闻爬取

```python
async def crawl_news():
    async with WebScrapingSpider() as spider:
        def extract_news_info(url, html):
            if not html:
                return None
            
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html, 'html.parser')
            
            title = soup.find('title')
            title_text = title.text.strip() if title else "无标题"
            
            return {
                'url': url,
                'title': title_text,
                'content_length': len(html),
                'crawl_time': time.time()
            }
        
        # 新闻网站URL列表
        news_urls = [
            "https://news.example.com/article1",
            "https://news.example.com/article2",
            # ... 更多URL
        ]
        
        # 批量爬取新闻
        news_data = await spider.crawl_with_callback(news_urls, extract_news_info)
        
        # 过滤有效数据
        valid_news = [news for news in news_data if news is not None]
        
        # 保存结果
        await spider.save_to_file(valid_news, "news_data.json")
        print(f"成功爬取 {len(valid_news)} 条新闻")
```

## 最佳实践

### 1. 错误处理

```python
async def robust_crawling():
    try:
        async with AsyncSpiderBase() as spider:
            response = await spider.get("https://example.com")
            if response:
                # 处理响应
                pass
            else:
                print("请求失败")
    except Exception as e:
        print(f"爬取过程中出现异常: {e}")
```

### 2. 速率控制

```python
# 设置合理的并发数和延迟
spider = AsyncSpiderBase(
    max_concurrent=3,        # 较低的并发数
    delay_range=(1.0, 3.0),  # 1-3秒的随机延迟
    retry_times=2            # 适度的重试次数
)
```

### 3. 资源管理

```python
# 使用异步上下文管理器确保资源正确释放
async with AsyncSpiderBase() as spider:
    # 爬取操作
    pass
# 会话会自动关闭
```

## 注意事项

1. **遵守robots.txt**: 爬取前检查目标网站的robots.txt文件
2. **合理设置延迟**: 避免对目标服务器造成过大压力
3. **处理反爬机制**: 可能需要设置代理、更换User-Agent等
4. **数据存储**: 及时保存爬取的数据，避免数据丢失
5. **法律合规**: 确保爬取行为符合相关法律法规

## 扩展开发

可以继承基础类来实现特定需求：

```python
class CustomSpider(AsyncSpiderBase):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 自定义初始化
    
    async def custom_method(self):
        # 自定义方法
        pass
```

这个异步爬虫基础类提供了强大而灵活的爬取能力，可以满足大多数爬虫需求。通过合理配置和使用，可以实现高效、稳定的数据爬取。
