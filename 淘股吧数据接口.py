from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import time
import atexit
import psutil
import os
import signal
import glob
import re


class TaoGuBaDataAPI:
    """
    淘股吧数据接口类
    提供淘股吧文章爬取功能
    """
    
    def __init__(self, username="spenser", password="eight986"):
        """
        初始化淘股吧数据接口
        
        Args:
            username (str): 登录用户名，默认"spenser"
            password (str): 登录密码，默认"eight986"
        """
        self.username = username
        self.password = password
        self.driver = None
        self.base_url = "https://www.taoguba.com.cn"
        
        # 注册程序退出时的清理函数
        atexit.register(self._cleanup_chrome_processes)
    
    def _cleanup_chrome_processes(self):
        """清理Chrome进程"""
        try:
            print("正在清理Chrome进程...")
            for proc in psutil.process_iter(['pid', 'name']):
                # 查找Chrome相关进程
                if any(chrome_process in proc.info['name'].lower() 
                      for chrome_process in ['chrome', 'chromedriver']):
                    try:
                        print(f"正在结束进程: {proc.info['name']} (PID: {proc.info['pid']})")
                        os.kill(proc.info['pid'], signal.SIGTERM)
                    except Exception as e:
                        print(f"结束进程失败: {str(e)}")
                        continue
            print("Chrome进程清理完成")
        except Exception as e:
            print(f"清理Chrome进程时出错: {str(e)}")
    
    def _init_driver(self):
        """初始化Chrome驱动"""
        if self.driver is not None:
            return True
        
        try:
            print("正在配置Chrome选项...")
            chrome_options = Options()
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_argument('--disable-background-networking')
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')
            chrome_options.add_argument('--start-maximized')
            
            # 添加User-Agent
            chrome_options.add_argument('user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36')
            
            # 添加实验性选项
            chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            print("正在创建Chrome驱动...")
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            self.driver.set_page_load_timeout(3)
            
            print("Chrome驱动初始化成功")
            return True
            
        except Exception as e:
            print(f"初始化Chrome驱动失败: {e}")
            return False
    
    def _wait_and_find_element(self, by, value, timeout=10):
        """等待并查找元素"""
        try:
            element = self.driver.find_element(by, value)
            print(f"成功找到元素: {by}={value}")
            return element
        except Exception as e:
            print(f"未找到元素: {by}={value}, 错误信息: {str(e)}")
            return None
    
    def _scroll_to_bottom(self, step=400, delay=0.01):
        """模拟渐进式滚动到页面底部"""
        print("开始模拟渐进式滚动到页面底部...")
        
        # 获取初始滚动高度
        total_height = self.driver.execute_script("return document.body.scrollHeight")
        current_position = 0
        
        while current_position < total_height:
            # 计算下一个滚动位置
            current_position += step
            
            # 滚动到新位置
            self.driver.execute_script(f"window.scrollTo(0, {current_position});")
            print(f"滚动到位置: {current_position}/{total_height}")
            
            # 等待内容加载
            time.sleep(delay)
            
            # 重新获取页面高度（可能因为图片加载而变化）
            new_height = self.driver.execute_script("return document.body.scrollHeight")
            if new_height > total_height:
                total_height = new_height
                print(f"页面高度更新为: {total_height}")
        
        # 最后确保滚动到底部
        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        print("已滚动到页面底部")
    
    def login(self, url):
        """
        登录淘股吧
        
        Args:
            url (str): 要访问的页面URL
            
        Returns:
            bool: 登录是否成功
        """
        if not self._init_driver():
            return False
        
        try:
            print(f"正在访问页面: {url}")
            try:
                self.driver.get(url)
            except TimeoutException:
                print("页面加载超时，继续执行...")
            
            # 等待页面基本加载
            print("等待页面基本加载 2 秒...")
            time.sleep(2)
            
            # 查找并点击登录/注册按钮
            print("正在查找登录/注册按钮...")
            login_btn = self._wait_and_find_element(By.CSS_SELECTOR, "a.header-a[onclick='loginPanel()']")
            if not login_btn:
                login_btn = self._wait_and_find_element(By.XPATH, "//a[@class='header-a' and @onclick='loginPanel()']")
            
            if login_btn:
                print("找到登录/注册按钮,准备点击...")
                self.driver.execute_script("arguments[0].click();", login_btn)
                print("已点击登录/注册按钮")
                
                # 等待账号登录按钮出现
                print("正在查找账号登录按钮...")
                account_login_btn = self._wait_and_find_element(By.ID, "userLoginBtn")
                if account_login_btn:
                    print("找到账号登录按钮,准备点击...")
                    account_login_btn.click()
                    print("已点击账号登录按钮")
                    
                    # 输入用户名
                    print("正在查找用户名输入框...")
                    username_input = self._wait_and_find_element(By.ID, "userPanelName")
                    if username_input:
                        print("找到用户名输入框,准备输入...")
                        username_input.clear()
                        username_input.send_keys(self.username)
                        print("已输入用户名")
                        time.sleep(1)
                    else:
                        print("未找到用户名输入框")
                    
                    # 输入密码
                    print("正在查找密码输入框...")
                    password_input = self._wait_and_find_element(By.ID, "userPanelPwd") 
                    if password_input:
                        print("找到密码输入框,准备输入...")
                        password_input.clear()
                        password_input.send_keys(self.password)
                        print("已输入密码")
                        time.sleep(1)
                    else:
                        print("未找到密码输入框")
                    
                    # 点击登录按钮
                    print("正在查找登录提交按钮...")
                    login_button = self._wait_and_find_element(By.ID, "loginBtn")
                    if login_button:
                        print("找到登录提交按钮,准备点击...")
                        login_button.click()
                        print("已点击登录提交按钮")
                        time.sleep(2)
                        return True
                    else:
                        print("未找到登录提交按钮")
                else:
                    print("未找到账号登录按钮")
            else:
                print("未找到登录/注册按钮")
            
            return False
            
        except Exception as e:
            print(f"登录过程中发生错误: {str(e)}")
            return False
    
    def extract_article_content(self, url):
        """
        提取文章内容
        
        Args:
            url (str): 文章URL
            
        Returns:
            dict: 包含文章信息的字典，如果失败返回None
        """
        if self.driver is None:
            print("驱动未初始化，请先调用login方法")
            return None
        
        try:
            print(f"正在访问文章页面: {url}")
            self.driver.get(url)
            time.sleep(2)
            
            # 滚动到底部加载完整内容
            self._scroll_to_bottom()
            print("页面滚动完成，开始获取内容...")
            
            # 获取标题
            title = "未知标题"
            try:
                title_element = self.driver.find_element(By.ID, "stockTitle")
                if title_element:
                    title = title_element.text.strip()
                    print(f"通过ID成功获取标题: {title}")
            except:
                try:
                    title_element = self.driver.find_element(By.CLASS_NAME, "article-tittle")
                    if title_element:
                        title = title_element.text.strip()
                        print(f"通过class成功获取标题: {title}")
                except:
                    try:
                        title = self.driver.execute_script("""
                            var titleElement = document.querySelector('.article-tittle');
                            return titleElement ? titleElement.textContent.trim() : '未知标题';
                        """)
                        if title != "未知标题":
                            print(f"通过JavaScript成功获取标题: {title}")
                    except Exception as e:
                        print(f"获取标题失败: {str(e)}")
            
            # 获取作者信息
            username = "未知作者"
            try:
                author_element = self.driver.find_element(By.CLASS_NAME, "article-author")
                if author_element:
                    username = author_element.text.strip()
                    print(f"成功获取作者: {username}")
            except Exception as e:
                print(f"获取作者信息失败: {str(e)}")
            
            # 获取正文内容
            content = "<p>未找到内容</p>"
            article_content = self._wait_and_find_element(By.CLASS_NAME, "article-text")
            if article_content:
                content = article_content.get_attribute('innerHTML')
                print("成功获取正文内容")
            else:
                print("未找到正文内容")
            
            # 获取发布时间
            publish_date = "未知日期"
            try:
                date_element = self.driver.find_element(By.XPATH, "//div[@class='article-data']//span[contains(text(), '-')]")
                if date_element:
                    date_text = date_element.text.strip()
                    date_match = re.search(r'\d{4}-\d{2}-\d{2}', date_text)
                    if date_match:
                        publish_date = date_match.group()
                        print(f"成功获取发布日期: {publish_date}")
            except Exception as e:
                print(f"获取发布日期失败: {str(e)}")
            
            return {
                'title': title,
                'author': username,
                'content': content,
                'publish_date': publish_date,
                'url': url
            }
            
        except Exception as e:
            print(f"提取文章内容失败: {str(e)}")
            return None

    def save_article_to_html(self, article_data, output_dir="articles"):
        """
        保存文章到HTML文件

        Args:
            article_data (dict): 文章数据
            output_dir (str): 输出目录，默认"articles"

        Returns:
            str: 保存的文件路径，如果失败返回None
        """
        if not article_data:
            print("文章数据为空，无法保存")
            return None

        try:
            # 创建输出目录
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 生成文件名
            title = article_data['title'].replace(' ', '_').replace('/', '_')
            publish_date = article_data['publish_date']
            filename = f"{publish_date}_{title}.html"
            filepath = os.path.join(output_dir, filename)

            # 生成HTML内容
            html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>{article_data['title']}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0 auto;
            max-width: 800px;
            padding: 20px;
        }}
        h1 {{
            text-align: center;
            color: #333;
        }}
        .article-meta {{
            text-align: center;
            color: #666;
            margin-bottom: 30px;
        }}
        .article-content {{
            margin-top: 20px;
        }}
        .article-content p {{
            margin: 1em 0;
        }}
        .article-content br {{
            display: block;
        }}
    </style>
</head>
<body>
    <h1>{article_data['title']}</h1>
    <div class="article-meta">
        <p>作者: {article_data['author']}</p>
        <p>发布日期: {article_data['publish_date']}</p>
        <p>原文链接: <a href="{article_data['url']}" target="_blank">{article_data['url']}</a></p>
    </div>
    <div class="article-content">
        {article_data['content']}
    </div>
</body>
</html>"""

            # 保存文件
            with open(filepath, "w", encoding="utf-8") as f:
                f.write(html_content)

            print(f"文章已保存为: {filepath}")
            return filepath

        except Exception as e:
            print(f"保存文章失败: {e}")
            return None

    def get_article_links_from_file(self, pattern="article_links*.txt"):
        """
        从文件中获取文章链接

        Args:
            pattern (str): 文件名模式，默认"article_links*.txt"

        Returns:
            list: 文章链接列表
        """
        links = []
        for file_path in glob.glob(pattern):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    links.extend([line.strip() for line in f if line.strip()])
                print(f"从 {file_path} 读取到 {len(links)} 个链接")
            except Exception as e:
                print(f"读取文件 {file_path} 时出错: {str(e)}")
        return links

    def batch_crawl_articles(self, article_links=None, output_dir="articles"):
        """
        批量爬取文章

        Args:
            article_links (list): 文章链接列表，如果为None则从文件中读取
            output_dir (str): 输出目录，默认"articles"

        Returns:
            list: 成功处理的文章信息列表
        """
        if article_links is None:
            article_links = self.get_article_links_from_file()

        if not article_links:
            print("未找到任何文章链接")
            return []

        print(f"共找到 {len(article_links)} 个链接待处理")

        # 使用第一个链接进行登录
        if not self.login(article_links[0]):
            print("登录失败，无法继续爬取")
            return []

        processed_articles = []

        # 处理第一个链接（当前页面）
        print("\n正在处理第 1 个链接（当前页面）...")
        article_data = self.extract_article_content(article_links[0])
        if article_data:
            filepath = self.save_article_to_html(article_data, output_dir)
            if filepath:
                processed_articles.append({
                    'title': article_data['title'],
                    'url': article_links[0],
                    'filepath': filepath
                })

        # 处理剩余链接
        for i, url in enumerate(article_links[1:], 2):
            try:
                print(f"\n正在处理第 {i}/{len(article_links)} 个链接: {url}")
                self.driver.get(url)
                time.sleep(2)

                article_data = self.extract_article_content(url)
                if article_data:
                    filepath = self.save_article_to_html(article_data, output_dir)
                    if filepath:
                        processed_articles.append({
                            'title': article_data['title'],
                            'url': url,
                            'filepath': filepath
                        })

                time.sleep(1)  # 处理完一篇文章后稍作等待

            except Exception as e:
                print(f"处理链接 {url} 时出错: {str(e)}")
                continue

        # 保存处理记录
        self._save_processing_record(processed_articles)

        print(f"\n批量爬取完成，共处理 {len(processed_articles)} 篇文章")
        return processed_articles

    def _save_processing_record(self, processed_articles):
        """保存处理记录"""
        try:
            record_lines = []
            for article in processed_articles:
                record_lines.append(f"{article['title']} - {article['url']}")

            with open("processed_articles.txt", "w", encoding="utf-8") as f:
                f.write("\n".join(record_lines))
            print("处理完成的文章列表已保存到 processed_articles.txt")
        except Exception as e:
            print(f"保存处理记录时出错: {str(e)}")

    def close(self):
        """关闭浏览器并清理资源"""
        try:
            if self.driver:
                print("正在关闭浏览器...")
                self.driver.quit()
                self.driver = None
                print("浏览器已关闭")
            self._cleanup_chrome_processes()
        except Exception as e:
            print(f"关闭浏览器时出错: {str(e)}")
            self._cleanup_chrome_processes()

    def __del__(self):
        """析构函数，确保资源被正确释放"""
        self.close()


# 使用示例
if __name__ == "__main__":
    # 创建淘股吧数据接口实例
    tgb_api = TaoGuBaDataAPI()

    try:
        # 示例1: 爬取单篇文章
        print("=== 爬取单篇文章示例 ===")
        test_url = "https://www.taoguba.com.cn/Article/3742583/1"

        # 登录
        if tgb_api.login(test_url):
            print("登录成功")

            # 提取文章内容
            article_data = tgb_api.extract_article_content(test_url)
            if article_data:
                print(f"文章标题: {article_data['title']}")
                print(f"文章作者: {article_data['author']}")
                print(f"发布日期: {article_data['publish_date']}")

                # 保存文章
                filepath = tgb_api.save_article_to_html(article_data)
                if filepath:
                    print(f"文章已保存到: {filepath}")

        # 示例2: 批量爬取文章（需要先准备article_links*.txt文件）
        # print("\n=== 批量爬取文章示例 ===")
        # processed_articles = tgb_api.batch_crawl_articles()
        # print(f"批量爬取完成，共处理 {len(processed_articles)} 篇文章")

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    finally:
        # 确保资源被正确释放
        tgb_api.close()
