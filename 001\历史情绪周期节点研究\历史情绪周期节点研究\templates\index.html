<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据可视化</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        :root {
            /* 颜色变量定义 */
            --color-percent1: #ee45d2;
            --color-percent2: #ee6570;
            --color-percent3: #a35c92;
            --color-percent3-plus: #734675; /* 新增：值为3及以上百分比颜色 */
            --color-max-value: #0f6fbe;
            --color-max-value-2: #36a6f0;
            --color-next-open: #6fe62a;
            --color-next-close: #f1dc1f;
            --color-result1-open: #ffa216;
            --color-result1-close:#fc232e;
            --color-result2-open: #67c6e6;
            --color-result2-close: #156d95;
            --color-result3-open: #e138ff;
            --color-result3-close: #190824;
            --color-area-fill: rgba(228, 191, 177, 0.3);
            --color-zero-line: #333;
            --color-zero-bg: #f8f8f8;
            --color-count1: #db39e0;
            --color-count2: #5d8fa8;
            --color-count3: #87b96c;
            --color-count3-plus: #a1a1a1; /* 新增：值为3及以上数量颜色 */
            --color-up-ratio: #fc9975; /* 新增：上涨比例颜色 */
            --color-bj-up-ratio: #63ce1c; /* 新增：竞价上涨比例颜色 */
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            width: 100%;
            max-width: 100%;
            margin: 0 auto;
            background-color: #fff;
            padding: 0;
            box-sizing: border-box;
        }
        #chart {
            width: 100%;
            height: 700px;
            margin-top: 0;
        }
        @media (max-width: 768px) {
            #chart {
                height: 500px;
            }
        }
        .controls {
            padding: 10px;
            margin: 10px 0;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .mark-date {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        select, button {
            padding: 6px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        #clearMarksBtn {
            background-color: #f44336;
        }
        
        #clearMarksBtn:hover {
            background-color: #d32f2f;
        }
        
        #markedDates p {
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .marked-dates-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .date-tag {
            display: inline-flex;
            align-items: center;
            background-color: #e8f5e9;
            border: 1px solid #c8e6c9;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .date-tag .delete-btn {
            margin-left: 6px;
            color: #f44336;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="controls">
            <div class="mark-date">
                <select id="dateSelect">
                    <option value="">-- 选择要标记的日期 --</option>
                </select>
                <button id="addMarkBtn">添加标记</button>
                <button id="clearMarksBtn">清除所有标记</button>
            </div>
            <div id="markedDates">
                <p>已标记日期: </p>
                <div id="markedDatesList" class="marked-dates-container"></div>
            </div>
        </div>
        <div id="chart"></div>
    </div>

    <script>
        // 从CSS变量获取颜色
        const getComputedColor = (varName) => {
            return getComputedStyle(document.documentElement).getPropertyValue(varName).trim();
        };
        
        // 颜色配置
        const colorConfig = {
            percent1: getComputedColor('--color-percent1'),
            percent2: getComputedColor('--color-percent2'),
            percent3: getComputedColor('--color-percent3'),
            percent3Plus: getComputedColor('--color-percent3-plus'), // 新增：值为3及以上百分比颜色
            maxValue: getComputedColor('--color-max-value'),
            maxValue2: getComputedColor('--color-max-value-2'),
            nextOpen: getComputedColor('--color-next-open'),
            nextClose: getComputedColor('--color-next-close'),
            result1Open: getComputedColor('--color-result1-open'),
            result1Close: getComputedColor('--color-result1-close'),
            result2Open: getComputedColor('--color-result2-open'),
            result2Close: getComputedColor('--color-result2-close'),
            result3Open: getComputedColor('--color-result3-open'),
            result3Close: getComputedColor('--color-result3-close'),
            areaFill: getComputedColor('--color-area-fill'),
            zeroLine: getComputedColor('--color-zero-line'),
            zeroBg: getComputedColor('--color-zero-bg'),
            count1: getComputedColor('--color-count1'),
            count2: getComputedColor('--color-count2'),
            count3: getComputedColor('--color-count3'),
            count3Plus: getComputedColor('--color-count3-plus'), // 新增：值为3及以上数量颜色
            upRatio: getComputedColor('--color-up-ratio'), // 新增：上涨比例颜色
            bjUpRatio: getComputedColor('--color-bj-up-ratio') // 新增：竞价上涨比例颜色
        };
        
        // 初始化图表
        const chartDom = document.getElementById('chart');
        const myChart = echarts.init(chartDom);
        
        // 显示加载中
        myChart.showLoading();
        
        // 从后端获取数据
        fetch('/data')
            .then(response => response.json())
            .then(data => {
                myChart.hideLoading();
                
                if (data.error) {
                    alert('获取数据出错: ' + data.error);
                    return;
                }
                
                // 初始化标记日期的下拉菜单
                const dateSelect = document.getElementById('dateSelect');
                
                // 填充日期下拉选择框
                data.dates.forEach((date, index) => {
                    const option = document.createElement('option');
                    option.value = index;
                    option.textContent = date;
                    dateSelect.appendChild(option);
                });
                
                // 初始化标记位置数组 - 现在为空，通过用户交互添加
                let markLinePositions = [];
                
                // 从服务器加载标记
                fetch('/load_marks')
                    .then(response => response.json())
                    .then(result => {
                        if (result.marks && Array.isArray(result.marks)) {
                            markLinePositions = result.marks;
                            updateChart(false); // 初始加载时不需要保持状态
                            updateMarkedDatesList();
                        }
                    })
                    .catch(error => {
                        console.error('加载标记出错:', error);
                    });
                
                // 保存标记到服务器
                function saveMarksToServer() {
                    fetch('/save_marks', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(markLinePositions)
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (!result.success) {
                            console.error('保存标记失败:', result.error);
                        }
                    })
                    .catch(error => {
                        console.error('保存标记出错:', error);
                    });
                }
                
                // 更新已标记日期的显示
                function updateMarkedDatesList() {
                    const markedDatesList = document.getElementById('markedDatesList');
                    markedDatesList.innerHTML = '';
                    
                    if (markLinePositions.length === 0) {
                        const noMarks = document.createElement('span');
                        noMarks.textContent = '无';
                        markedDatesList.appendChild(noMarks);
                    } else {
                        markLinePositions.forEach((index, i) => {
                            if (data.dates[index]) {
                                const dateTag = document.createElement('div');
                                dateTag.className = 'date-tag';
                                dateTag.innerHTML = data.dates[index] + 
                                    '<span class="delete-btn" data-index="' + i + '">×</span>';
                                markedDatesList.appendChild(dateTag);
                                
                                // 添加删除按钮点击事件
                                dateTag.querySelector('.delete-btn').addEventListener('click', function() {
                                    const idx = parseInt(this.getAttribute('data-index'));
                                    markLinePositions.splice(idx, 1);
                                    updateChart(true); // 保持当前视图状态
                                    updateMarkedDatesList();
                                    saveMarksToServer();
                                });
                            }
                        });
                    }
                }
                
                // 初始化显示
                updateMarkedDatesList();
                
                // 添加标记按钮点击事件
                document.getElementById('addMarkBtn').addEventListener('click', function() {
                    const selectedIndex = parseInt(dateSelect.value);
                    if (!isNaN(selectedIndex) && !markLinePositions.includes(selectedIndex)) {
                        markLinePositions.push(selectedIndex);
                        
                        // 保存当前的缩放状态
                        const currentDataZoom = myChart.getOption().dataZoom;
                        const currentStart = currentDataZoom[0].start;
                        const currentEnd = currentDataZoom[0].end;
                        const zoomRange = currentEnd - currentStart;
                        
                        // 计算新的缩放范围，使新标记位置在视图中间
                        const totalPoints = data.dates.length;
                        const percentPosition = (selectedIndex / totalPoints) * 100;
                        
                        // 确保新添加的标记在视图中可见
                        let newStart, newEnd;
                        
                        // 如果当前缩放范围包含了新标记，则保持当前缩放
                        if (percentPosition >= currentStart && percentPosition <= currentEnd) {
                            newStart = currentStart;
                            newEnd = currentEnd;
                        } else {
                            // 否则，将视图居中在新标记位置
                            newStart = Math.max(0, percentPosition - zoomRange / 2);
                            newEnd = Math.min(100, newStart + zoomRange);
                            
                            // 调整起始位置，确保范围不超过100%
                            if (newEnd > 100) {
                                newStart = Math.max(0, 100 - zoomRange);
                                newEnd = 100;
                            }
                        }
                        
                        // 更新缩放状态
                        option.dataZoom[0].start = newStart;
                        option.dataZoom[0].end = newEnd;
                        option.dataZoom[1].start = newStart;
                        option.dataZoom[1].end = newEnd;
                        
                        updateChart(false); // 使用新的缩放状态
                        updateMarkedDatesList();
                        saveMarksToServer();
                    }
                });
                
                // 清除所有标记按钮点击事件
                document.getElementById('clearMarksBtn').addEventListener('click', function() {
                    markLinePositions = [];
                    updateChart(true); // 保持当前视图状态
                    updateMarkedDatesList();
                    saveMarksToServer();
                });
                
                // 更新图表函数
                function updateChart(keepState = true) {
                    // 保存当前的缩放状态
                    const currentDataZoom = myChart.getOption().dataZoom;
                    const currentStart = currentDataZoom[0].start;
                    const currentEnd = currentDataZoom[0].end;
                    
                    // 修改标记线数据
                    option.series[option.series.length - 1].markLine.data = markLinePositions.map(index => ({
                        xAxis: index
                    }));
                    
                    // 保持缩放状态
                    if (keepState) {
                        option.dataZoom[0].start = currentStart;
                        option.dataZoom[0].end = currentEnd;
                        option.dataZoom[1].start = currentStart;
                        option.dataZoom[1].end = currentEnd;
                    }
                    
                    // 重新设置图表选项
                    myChart.setOption(option);
                }
                
                // 配置图表选项
                const option = {
                    title: {
                        left: 'center',
                        top: 0
                    },
                    tooltip: {
                        show: true,
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            lineStyle: {
                                color: '#999',
                                width: 1
                            }
                        },
                        position: function() {
                            // 固定在右下角
                            return ['90%', '85%'];
                        },
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        borderWidth: 1,
                        borderColor: '#ccc',
                        textStyle: {
                            color: '#333',
                            fontSize: 12
                        },
                        padding: [5, 10],
                        shadowBlur: 0,
                        shadowColor: 'rgba(0, 0, 0, 0)',
                        shadowOffsetX: 0,
                        shadowOffsetY: 0,
                        extraCssText: 'box-shadow: 0 0 0 rgba(0, 0, 0, 0);'
                    },
                    legend: {
                        data: [
                            '值为1的百分比', '值为2的百分比', '值为3的百分比', '值为3及以上的百分比',
                            '最大值', '最高2',
                            '次日竞价涨幅平均值', '次日涨幅平均值', '涨幅差值区域', '差值基线',
                            '天板结果为1的次日竞价涨幅', '天板结果为1的次日涨幅',
                            '天板结果为2的次日竞价涨幅', '天板结果为2的次日涨幅',
                            '天板结果≥3的次日竞价涨幅', '天板结果≥3的次日涨幅',
                            '值为1的数量', '值为2的数量', '值为3的数量', '值为3及以上的数量',
                            '上涨比例', '竞价上涨比例'
                        ],
                        top: 10,
                        left: 'center',
                        textStyle: {
                            fontSize: 10
                        },
                        selected: {
                            '值为1的百分比': false,  // 默认隐藏"值为1的百分比"曲线
                            '值为2的百分比': false,  // 默认隐藏"值为2的百分比"曲线
                            '值为3的百分比': false,   // 默认隐藏"值为3的百分比"曲线
                            '值为3及以上的百分比': false,  // 默认隐藏"值为3及以上的百分比"曲线
                            '次日竞价涨幅平均值': false,  // 默认隐藏"次日竞价涨幅平均值"曲线
                            '次日涨幅平均值': false,   // 默认隐藏"次日涨幅平均值"曲线
                            '涨幅差值区域': false,   // 默认隐藏"涨幅差值区域"
                            '差值基线': false,   // 默认隐藏"差值基线"
                            '上涨比例': true,   // 默认显示"上涨比例"
                            '竞价上涨比例': true // 新增，默认显示
                        }
                    },
                    xAxis: {
                        type: 'category',
                        data: data.dates,
                        axisLabel: {
                            rotate: 45,
                            interval: 'auto',
                            fontSize: 10
                        }
                    },
                    yAxis: [
                        {
                            type: 'value',
                            name: '百分比 (%)',
                            min: 0,
                            position: 'left',
                            axisLine: {
                                lineStyle: {
                                    color: colorConfig.percent1
                                }
                            },
                            splitLine: {
                                lineStyle: {
                                    type: 'dashed'
                                }
                            },
                            nameLocation: 'middle',
                            nameGap: 40
                        },
                        {
                            type: 'value',
                            name: '最大值/涨幅 (%)',
                            position: 'right',
                            axisLine: {
                                lineStyle: {
                                    color: colorConfig.maxValue
                                }
                            },
                            splitLine: {
                                show: false
                            },
                            axisLabel: {
                                formatter: function(value) {
                                    if (value === 0) {
                                        return '{zero|' + value + '}';
                                    }
                                    return value;
                                },
                                rich: {
                                    zero: {
                                        color: colorConfig.zeroLine,
                                        fontWeight: 'bold',
                                        backgroundColor: colorConfig.zeroBg,
                                        padding: [2, 4],
                                        borderRadius: 2
                                    }
                                }
                            },
                            nameLocation: 'middle',
                            nameGap: 45
                        },
                        {
                            type: 'value',
                            name: '数量',
                            position: 'right',
                            offset: 40,
                            axisLine: {
                                lineStyle: {
                                    color: colorConfig.count1
                                }
                            },
                            splitLine: {
                                show: false
                            },
                            min: 0,
                            nameLocation: 'middle',
                            nameGap: 30
                        }
                    ],
                    dataZoom: [
                        {
                            type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            start: 0,
                            end: 50,
                            height: 20,
                            bottom: 10,
                            handleSize: 20,
                            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                            handleStyle: {
                                color: '#fff',
                                shadowBlur: 3,
                                shadowColor: 'rgba(0, 0, 0, 0.6)',
                                shadowOffsetX: 2,
                                shadowOffsetY: 2
                            }
                        },
                        {
                            type: 'inside',
                            xAxisIndex: [0],
                            start: 0,
                            end: 50,
                            zoomOnMouseWheel: true,
                            moveOnMouseMove: true
                        }
                    ],
                    series: [
                        {
                            name: '值为1的百分比',
                            type: 'line',
                            data: data.count_1_percent,
                            yAxisIndex: 0,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.percent1
                            },
                            connectNulls: true,
                            markLine: {
                                silent: true,
                                symbol: 'none',
                                lineStyle: {
                                    color: colorConfig.zeroLine,
                                    width: 2,
                                    type: 'solid'
                                },
                                data: [
                                    {
                                        yAxis: 0,
                                        label: {
                                            show: true,
                                            position: 'start',
                                            formatter: '0'
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            name: '值为2的百分比',
                            type: 'line',
                            data: data.count_2_percent,
                            yAxisIndex: 0,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.percent2
                            },
                            connectNulls: true
                        },
                        {
                            name: '最大值',
                            type: 'line',
                            data: data.max_values,
                            yAxisIndex: 1,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.maxValue
                            },
                            connectNulls: true,
                            label: {
                                show: true,
                                position: 'top',
                                formatter: function(params) {
                                    return params.value === null ? '' : params.value;
                                },
                                fontSize: 9,
                                color: colorConfig.maxValue,
                                backgroundColor: 'rgba(255,255,255,0.7)',
                                padding: [2, 3],
                                borderRadius: 2
                            },
                            markLine: {
                                silent: true,
                                symbol: 'none',
                                lineStyle: {
                                    color: colorConfig.zeroLine,
                                    width: 2,
                                    type: 'solid'
                                },
                                data: [
                                    {
                                        yAxis: 0,
                                        label: {
                                            show: true,
                                            position: 'end',
                                            formatter: '0',
                                            fontSize: 12,
                                            fontWeight: 'bold',
                                            backgroundColor: colorConfig.zeroBg,
                                            padding: [2, 4],
                                            borderRadius: 2
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            name: '最高2',
                            type: 'line',
                            data: data.max_values_2,
                            yAxisIndex: 1,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.maxValue2
                            },
                            connectNulls: true,
                            label: {
                                show: true,
                                position: 'bottom',
                                formatter: function(params) {
                                    return params.value === null ? '' : params.value;
                                },
                                fontSize: 9,
                                color: colorConfig.maxValue2,
                                backgroundColor: 'rgba(255,255,255,0.7)',
                                padding: [2, 3],
                                borderRadius: 2
                            }
                        },
                        {
                            name: '次日竞价涨幅平均值',
                            type: 'line',
                            data: data.next_day_open_avg,
                            yAxisIndex: 1,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.nextOpen
                            },
                            connectNulls: true,
                            lineStyle: {
                                width: 2
                            }
                        },
                        {
                            name: '次日涨幅平均值',
                            type: 'line',
                            data: data.next_day_close_avg,
                            yAxisIndex: 1,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.nextClose
                            },
                            lineStyle: {
                                width: 2
                            },
                            connectNulls: true
                        },
                        {
                            name: '涨幅差值区域',
                            type: 'line',
                            data: data.next_day_close_avg,
                            yAxisIndex: 1,
                            symbol: 'none',
                            showSymbol: false,
                            lineStyle: {
                                width: 0,
                                opacity: 0
                            },
                            areaStyle: {
                                opacity: 0.3,
                                color: colorConfig.nextClose,
                                origin: 'start'
                            },
                            connectNulls: true,
                            stack: 'difference',
                            legendHoverLink: false
                        },
                        {
                            name: '差值基线',
                            type: 'line',
                            data: data.difference_values,
                            yAxisIndex: 1,
                            symbol: 'none',
                            showSymbol: false,
                            lineStyle: {
                                width: 0,
                                opacity: 0
                            },
                            stack: 'difference',
                            connectNulls: true,
                            itemStyle: {
                                opacity: 0
                            },
                            legendHoverLink: false
                        },
                        {
                            name: '值为1的数量',
                            type: 'line',
                            data: data.count_1,
                            yAxisIndex: 2,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.count1
                            },
                            lineStyle: {
                                width: 2,
                                type: 'dashed'
                            },
                            connectNulls: true
                        },
                        {
                            name: '值为2的数量',
                            type: 'line',
                            data: data.count_2,
                            yAxisIndex: 2,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.count2
                            },
                            lineStyle: {
                                width: 2,
                                type: 'dashed'
                            },
                            connectNulls: true
                        },
                        {
                            name: '值为3的数量',
                            type: 'line',
                            data: data.count_3,
                            yAxisIndex: 2,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.count3
                            },
                            lineStyle: {
                                width: 2,
                                type: 'dashed'
                            },
                            connectNulls: true
                        },
                        {
                            name: '值为3的百分比',
                            type: 'line',
                            data: data.count_3_percent,
                            yAxisIndex: 0,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.percent3
                            },
                            connectNulls: true
                        },
                        {
                            name: '值为3及以上的百分比',
                            type: 'line',
                            data: data.count_3_plus_percent,
                            yAxisIndex: 0,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.percent3Plus
                            },
                            connectNulls: true
                        },
                        {
                            name: '天板结果为1的次日竞价涨幅',
                            type: 'line',
                            data: data.result1_next_open_avg,
                            yAxisIndex: 1,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.result1Open
                            },
                            lineStyle: {
                                width: 2,
                                type: 'dotted'
                            },
                            connectNulls: true
                        },
                        {
                            name: '天板结果为1的次日涨幅',
                            type: 'line',
                            data: data.result1_next_close_avg,
                            yAxisIndex: 1,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.result1Close
                            },
                            lineStyle: {
                                width: 2,
                                type: 'dotted'
                            },
                            connectNulls: true
                        },
                        {
                            name: '天板结果为2的次日竞价涨幅',
                            type: 'line',
                            data: data.result2_next_open_avg,
                            yAxisIndex: 1,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.result2Open
                            },
                            lineStyle: {
                                width: 2,
                                type: 'dotted'
                            },
                            connectNulls: true
                        },
                        {
                            name: '天板结果为2的次日涨幅',
                            type: 'line',
                            data: data.result2_next_close_avg,
                            yAxisIndex: 1,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.result2Close
                            },
                            lineStyle: {
                                width: 2,
                                type: 'dotted'
                            },
                            connectNulls: true
                        },
                        {
                            name: '天板结果≥3的次日竞价涨幅',
                            type: 'line',
                            data: data.result3_next_open_avg,
                            yAxisIndex: 1,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.result3Open
                            },
                            lineStyle: {
                                width: 2,
                                type: 'dotted'
                            },
                            connectNulls: true
                        },
                        {
                            name: '天板结果≥3的次日涨幅',
                            type: 'line',
                            data: data.result3_next_close_avg,
                            yAxisIndex: 1,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.result3Close
                            },
                            lineStyle: {
                                width: 2,
                                type: 'dotted'
                            },
                            connectNulls: true
                        },
                        {
                            name: '值为3及以上的数量',
                            type: 'line',
                            data: data.count_3_plus,
                            yAxisIndex: 2,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.count3Plus
                            },
                            lineStyle: {
                                width: 2,
                                type: 'dashed'
                            },
                            connectNulls: true
                        },
                        {
                            name: '上涨比例',
                            type: 'line',
                            data: data.up_ratio,
                            yAxisIndex: 0,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.upRatio
                            },
                            connectNulls: true
                        },
                        {
                            name: '竞价上涨比例',
                            type: 'line',
                            data: data.bj_up_ratio,
                            yAxisIndex: 0,
                            symbol: 'circle',
                            symbolSize: 4,
                            itemStyle: {
                                color: colorConfig.bjUpRatio // 独立颜色
                            },
                            connectNulls: true
                        },
                        // 添加日期竖线标记系列
                        {
                            name: '日期标记',
                            type: 'line',
                            markLine: {
                                silent: true,
                                symbol: ['none', 'none'],
                                label: {
                                    show: true,
                                    position: 'start',
                                    formatter: function(params) {
                                        // 返回对应的日期作为标签
                                        const index = markLinePositions.indexOf(params.value);
                                        if (index !== -1 && data.dates[params.value]) {
                                            return data.dates[params.value];
                                        }
                                        return '';
                                    },
                                    fontSize: 10,
                                    color: '#e74c3c',
                                    backgroundColor: 'rgba(255,255,255,0.8)',
                                    padding: [2, 4],
                                    borderRadius: 2
                                },
                                lineStyle: {
                                    color: '#e74c3c',
                                    type: 'dashed',
                                    width: 1.5
                                },
                                data: markLinePositions.map(index => ({
                                    xAxis: index
                                }))
                            },
                            data: []
                        }
                    ],
                    grid: {
                        left: 0,
                        right: 0,
                        bottom: '15%',
                        top: '15%',
                        containLabel: true
                    }
                };
                
                // 设置图表
                myChart.setOption(option);
                
                // 响应窗口大小变化
                window.addEventListener('resize', function() {
                    myChart.resize();
                });
            })
            .catch(error => {
                myChart.hideLoading();
                console.error('获取数据出错:', error);
                alert('获取数据出错: ' + error.message);
            });
    </script>
</body>
</html> 