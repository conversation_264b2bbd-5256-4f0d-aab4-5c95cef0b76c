"""
股票数据爬虫示例
使用异步爬虫基础类爬取股票相关数据
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any
from aiohttp_base import AsyncSpiderBase
from API爬虫类 import APISpider


class StockDataSpider(AsyncSpiderBase):
    """
    股票数据爬虫类
    继承自AsyncSpiderBase，专门用于爬取股票数据
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 添加股票数据爬取专用的请求头
        self.add_header('Referer', 'https://finance.sina.com.cn/')
        self.add_header('Accept', 'application/json, text/javascript, */*; q=0.01')
    
    async def get_stock_basic_info(self, stock_codes: List[str]) -> List[Dict[str, Any]]:
        """
        获取股票基本信息
        
        Args:
            stock_codes (List[str]): 股票代码列表
            
        Returns:
            List[Dict]: 股票基本信息列表
        """
        def process_stock_response(url, response_text):
            """处理股票响应数据"""
            if not response_text:
                return None
            
            try:
                # 解析新浪财经的股票数据格式
                # 格式通常是: var hq_str_sh000001="平安银行,10.50,10.48,10.52,..."
                if 'hq_str_' in response_text:
                    lines = response_text.strip().split('\n')
                    stock_data = []
                    
                    for line in lines:
                        if 'hq_str_' in line and '=' in line:
                            # 提取股票代码
                            code_part = line.split('hq_str_')[1].split('=')[0]
                            
                            # 提取数据部分
                            data_part = line.split('"')[1] if '"' in line else ""
                            
                            if data_part:
                                fields = data_part.split(',')
                                if len(fields) >= 6:
                                    stock_info = {
                                        'code': code_part,
                                        'name': fields[0],
                                        'current_price': float(fields[3]) if fields[3] else 0,
                                        'yesterday_close': float(fields[2]) if fields[2] else 0,
                                        'today_open': float(fields[1]) if fields[1] else 0,
                                        'today_high': float(fields[4]) if fields[4] else 0,
                                        'today_low': float(fields[5]) if fields[5] else 0,
                                        'volume': int(fields[8]) if len(fields) > 8 and fields[8] else 0,
                                        'amount': float(fields[9]) if len(fields) > 9 and fields[9] else 0,
                                        'update_time': datetime.now().isoformat()
                                    }
                                    
                                    # 计算涨跌幅
                                    if stock_info['yesterday_close'] > 0:
                                        change = stock_info['current_price'] - stock_info['yesterday_close']
                                        change_percent = (change / stock_info['yesterday_close']) * 100
                                        stock_info['change'] = round(change, 2)
                                        stock_info['change_percent'] = round(change_percent, 2)
                                    
                                    stock_data.append(stock_info)
                    
                    return stock_data
                
            except Exception as e:
                self.logger.error(f"解析股票数据失败: {e}")
            
            return None
        
        # 构建新浪财经API URL
        # 格式: http://hq.sinajs.cn/list=sh000001,sz000002
        formatted_codes = []
        for code in stock_codes:
            if code.startswith('6'):
                formatted_codes.append(f'sh{code}')
            elif code.startswith(('0', '3')):
                formatted_codes.append(f'sz{code}')
            else:
                formatted_codes.append(code)
        
        codes_str = ','.join(formatted_codes)
        url = f"http://hq.sinajs.cn/list={codes_str}"
        
        self.logger.info(f"获取股票基本信息: {stock_codes}")
        
        # 使用回调函数处理响应
        results = await self.crawl_with_callback([url], process_stock_response)
        
        if results and results[0]:
            return results[0]
        
        return []
    
    async def get_stock_history(self, stock_code: str, days: int = 30) -> List[Dict[str, Any]]:
        """
        获取股票历史数据
        
        Args:
            stock_code (str): 股票代码
            days (int): 获取天数，默认30天
            
        Returns:
            List[Dict]: 历史数据列表
        """
        # 这里可以接入其他数据源的历史数据API
        # 示例使用模拟数据
        self.logger.info(f"获取股票 {stock_code} 的 {days} 天历史数据")
        
        history_data = []
        base_price = 10.0
        
        for i in range(days):
            date = datetime.now() - timedelta(days=days-i-1)
            # 模拟价格波动
            import random
            price_change = random.uniform(-0.5, 0.5)
            base_price += price_change
            
            history_data.append({
                'date': date.strftime('%Y-%m-%d'),
                'open': round(base_price + random.uniform(-0.2, 0.2), 2),
                'high': round(base_price + random.uniform(0, 0.5), 2),
                'low': round(base_price - random.uniform(0, 0.5), 2),
                'close': round(base_price, 2),
                'volume': random.randint(1000000, 10000000)
            })
        
        return history_data
    
    async def get_market_summary(self) -> Dict[str, Any]:
        """
        获取市场概况
        
        Returns:
            Dict: 市场概况数据
        """
        # 获取主要指数数据
        index_codes = ['sh000001', 'sz399001', 'sz399006']  # 上证指数、深证成指、创业板指
        
        url = f"http://hq.sinajs.cn/list={','.join(index_codes)}"
        
        def process_index_response(url, response_text):
            if not response_text:
                return None
            
            try:
                lines = response_text.strip().split('\n')
                index_data = {}
                
                for line in lines:
                    if 'hq_str_' in line and '=' in line:
                        code_part = line.split('hq_str_')[1].split('=')[0]
                        data_part = line.split('"')[1] if '"' in line else ""
                        
                        if data_part:
                            fields = data_part.split(',')
                            if len(fields) >= 6:
                                index_info = {
                                    'name': fields[0],
                                    'current': float(fields[1]) if fields[1] else 0,
                                    'change': float(fields[2]) if fields[2] else 0,
                                    'change_percent': float(fields[3]) if fields[3] else 0,
                                    'volume': int(fields[4]) if fields[4] else 0,
                                    'amount': float(fields[5]) if fields[5] else 0
                                }
                                index_data[code_part] = index_info
                
                return {
                    'indices': index_data,
                    'update_time': datetime.now().isoformat(),
                    'market_status': 'open' if 9 <= datetime.now().hour <= 15 else 'closed'
                }
                
            except Exception as e:
                self.logger.error(f"解析指数数据失败: {e}")
            
            return None
        
        results = await self.crawl_with_callback([url], process_index_response)
        
        if results and results[0]:
            return results[0]
        
        return {}
    
    async def batch_get_stock_data(self, stock_codes: List[str], batch_size: int = 50) -> List[Dict[str, Any]]:
        """
        批量获取股票数据
        
        Args:
            stock_codes (List[str]): 股票代码列表
            batch_size (int): 批次大小，默认50
            
        Returns:
            List[Dict]: 所有股票数据
        """
        all_stock_data = []
        
        # 分批处理
        for i in range(0, len(stock_codes), batch_size):
            batch_codes = stock_codes[i:i + batch_size]
            self.logger.info(f"处理第 {i//batch_size + 1} 批股票数据，共 {len(batch_codes)} 只")
            
            batch_data = await self.get_stock_basic_info(batch_codes)
            all_stock_data.extend(batch_data)
            
            # 批次间延迟
            if i + batch_size < len(stock_codes):
                await asyncio.sleep(1)
        
        return all_stock_data


async def stock_spider_example():
    """股票爬虫使用示例"""
    
    print("=== 股票数据爬虫示例 ===")
    
    # 创建股票爬虫实例
    async with StockDataSpider(max_concurrent=3, delay_range=(0.5, 1.0)) as spider:
        
        # 示例1: 获取单只股票信息
        print("\n1. 获取单只股票信息...")
        stock_data = await spider.get_stock_basic_info(['000001'])
        if stock_data:
            for stock in stock_data:
                print(f"   股票: {stock['name']} ({stock['code']})")
                print(f"   当前价: {stock['current_price']}")
                print(f"   涨跌幅: {stock.get('change_percent', 0):.2f}%")
        
        # 示例2: 批量获取多只股票信息
        print("\n2. 批量获取多只股票信息...")
        stock_codes = ['000001', '000002', '600000', '600036', '300015']
        batch_data = await spider.batch_get_stock_data(stock_codes)
        
        print(f"   成功获取 {len(batch_data)} 只股票数据:")
        for stock in batch_data[:3]:  # 只显示前3只
            print(f"   - {stock['name']}: {stock['current_price']} ({stock.get('change_percent', 0):+.2f}%)")
        
        # 示例3: 获取市场概况
        print("\n3. 获取市场概况...")
        market_data = await spider.get_market_summary()
        if market_data and 'indices' in market_data:
            print(f"   市场状态: {market_data.get('market_status', 'unknown')}")
            for code, index_info in market_data['indices'].items():
                print(f"   {index_info['name']}: {index_info['current']} ({index_info['change_percent']:+.2f}%)")
        
        # 示例4: 获取历史数据
        print("\n4. 获取历史数据...")
        history_data = await spider.get_stock_history('000001', days=5)
        print(f"   获取到 {len(history_data)} 天历史数据:")
        for day_data in history_data[-3:]:  # 显示最近3天
            print(f"   {day_data['date']}: 开盘 {day_data['open']}, 收盘 {day_data['close']}")
        
        # 示例5: 保存数据到文件
        print("\n5. 保存数据到文件...")
        all_data = {
            'market_summary': market_data,
            'stock_data': batch_data,
            'history_sample': history_data,
            'crawl_time': datetime.now().isoformat()
        }
        
        await spider.save_to_file(all_data, f"stock_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        print("   数据已保存到文件")


async def monitor_stock_prices():
    """股票价格监控示例"""
    
    print("\n=== 股票价格监控示例 ===")
    
    # 监控的股票列表
    watch_list = ['000001', '000002', '600000']
    
    async with StockDataSpider(max_concurrent=2) as spider:
        
        print(f"开始监控 {len(watch_list)} 只股票...")
        
        for round_num in range(3):  # 监控3轮
            print(f"\n第 {round_num + 1} 轮监控:")
            
            stock_data = await spider.get_stock_basic_info(watch_list)
            
            for stock in stock_data:
                change_percent = stock.get('change_percent', 0)
                status = "📈" if change_percent > 0 else "📉" if change_percent < 0 else "➡️"
                
                print(f"   {status} {stock['name']}: {stock['current_price']} ({change_percent:+.2f}%)")
            
            if round_num < 2:  # 最后一轮不需要等待
                print("   等待30秒后继续监控...")
                await asyncio.sleep(30)
        
        print("监控结束")


async def find_active_stocks():
    """寻找活跃股票示例"""
    
    print("\n=== 寻找活跃股票示例 ===")
    
    # 常见股票代码列表（实际应用中可以从文件或数据库读取）
    stock_codes = [
        '000001', '000002', '000858', '000876', '600000', 
        '600036', '600519', '600887', '300015', '300059'
    ]
    
    async with StockDataSpider(max_concurrent=5) as spider:
        
        print(f"分析 {len(stock_codes)} 只股票的活跃度...")
        
        stock_data = await spider.batch_get_stock_data(stock_codes)
        
        # 按涨跌幅排序
        active_stocks = sorted(
            [s for s in stock_data if s.get('change_percent') is not None],
            key=lambda x: abs(x['change_percent']),
            reverse=True
        )
        
        print("\n最活跃的股票 (按涨跌幅绝对值排序):")
        for i, stock in enumerate(active_stocks[:5], 1):
            change_percent = stock['change_percent']
            volume = stock.get('volume', 0)
            status = "🔥" if abs(change_percent) > 5 else "⚡" if abs(change_percent) > 2 else "📊"
            
            print(f"   {i}. {status} {stock['name']} ({stock['code']})")
            print(f"      价格: {stock['current_price']}, 涨跌: {change_percent:+.2f}%, 成交量: {volume:,}")


if __name__ == "__main__":
    # 运行股票爬虫示例
    print("股票数据爬虫演示")
    print("=" * 50)
    
    async def run_all_examples():
        await stock_spider_example()
        await monitor_stock_prices()
        await find_active_stocks()
    
    asyncio.run(run_all_examples())
