"""
量化数据接口集成
整合所有数据源的接口，提供统一的访问入口
"""

from 韭研数据接口 import JiuYanDataAPI
from 开盘啦数据接口 import KaiPanLaDataAPI
from 同花顺问财接口 import TongHuaShunWenCaiAPI
from 淘股吧数据接口 import TaoGuBaDataAPI
from 通达信联动接口 import TongDaXinLinkAPI
from datetime import datetime
import pandas as pd


class QuantDataIntegration:
    """
    量化数据接口集成类
    提供统一的数据访问接口
    """
    
    def __init__(self):
        """初始化所有数据源接口"""
        print("正在初始化量化数据接口集成...")
        
        # 初始化各个数据源接口
        self.jiuyan_api = JiuYanDataAPI()
        self.kaipaila_api = KaiPanLaDataAPI()
        self.wencai_api = TongHuaShunWenCaiAPI()
        self.taoguba_api = TaoGuBaDataAPI()
        self.tongdaxin_api = TongDaXinLinkAPI()
        
        print("量化数据接口集成初始化完成")
    
    def get_news_data(self, source="jiuyan", **kwargs):
        """
        获取新闻数据
        
        Args:
            source (str): 数据源，可选值: "jiuyan"
            **kwargs: 其他参数
            
        Returns:
            根据数据源返回相应的数据格式
        """
        if source == "jiuyan":
            return self.jiuyan_api.get_news_data(**kwargs)
        else:
            print(f"不支持的新闻数据源: {source}")
            return None
    
    def search_news(self, keyword, source="jiuyan", **kwargs):
        """
        搜索新闻
        
        Args:
            keyword (str): 搜索关键词
            source (str): 数据源，可选值: "jiuyan"
            **kwargs: 其他参数
            
        Returns:
            搜索结果列表
        """
        if source == "jiuyan":
            return self.jiuyan_api.search_news_by_keyword(keyword, **kwargs)
        else:
            print(f"不支持的新闻搜索数据源: {source}")
            return []
    
    def get_stock_data(self, data_type, source="kaipaila", **kwargs):
        """
        获取股票数据
        
        Args:
            data_type (str): 数据类型，如"selected", "limit_up", "auction"等
            source (str): 数据源，可选值: "kaipaila", "wencai"
            **kwargs: 其他参数
            
        Returns:
            股票数据
        """
        if source == "kaipaila":
            if data_type == "selected":
                return self.kaipaila_api.get_selected_stocks(**kwargs)
            elif data_type == "limit_up":
                return self.kaipaila_api.get_limit_up_stocks(**kwargs)
            elif data_type == "auction":
                return self.kaipaila_api.get_auction_data(**kwargs)
            elif data_type == "dragon_tiger":
                return self.kaipaila_api.get_dragon_tiger_list(**kwargs)
            elif data_type == "sector_strength":
                return self.kaipaila_api.get_sector_strength(**kwargs)
            elif data_type == "big_orders":
                return self.kaipaila_api.get_big_orders(**kwargs)
            elif data_type == "popular":
                return self.kaipaila_api.get_popular_stocks(**kwargs)
            else:
                print(f"开盘啦不支持的数据类型: {data_type}")
                return None
        
        elif source == "wencai":
            if data_type == "limit_up":
                return self.wencai_api.query_limit_up_stocks(**kwargs)
            elif data_type == "auction":
                return self.wencai_api.query_auction_data(**kwargs)
            elif data_type == "new_high":
                return self.wencai_api.query_new_high_stocks(**kwargs)
            elif data_type == "volume_ratio":
                return self.wencai_api.query_volume_ratio_stocks(**kwargs)
            elif data_type == "turnover_rate":
                return self.wencai_api.query_turnover_rate_stocks(**kwargs)
            elif data_type == "price_change":
                return self.wencai_api.query_price_change_stocks(**kwargs)
            else:
                print(f"同花顺问财不支持的数据类型: {data_type}")
                return None
        
        else:
            print(f"不支持的股票数据源: {source}")
            return None
    
    def get_stock_reason(self, stock_id, source="kaipaila"):
        """
        获取股票涨停原因
        
        Args:
            stock_id (str): 股票代码
            source (str): 数据源，可选值: "kaipaila"
            
        Returns:
            涨停原因数据
        """
        if source == "kaipaila":
            return self.kaipaila_api.get_limit_up_reason(stock_id)
        else:
            print(f"不支持的涨停原因数据源: {source}")
            return None
    
    def crawl_articles(self, article_links=None, source="taoguba", **kwargs):
        """
        爬取文章
        
        Args:
            article_links (list): 文章链接列表
            source (str): 数据源，可选值: "taoguba"
            **kwargs: 其他参数
            
        Returns:
            爬取结果
        """
        if source == "taoguba":
            return self.taoguba_api.batch_crawl_articles(article_links, **kwargs)
        else:
            print(f"不支持的文章爬取数据源: {source}")
            return []
    
    def send_stock_code(self, stock_code, target="tonghuashun"):
        """
        发送股票代码到交易软件
        
        Args:
            stock_code (str): 股票代码
            target (str): 目标软件，可选值: "tonghuashun"
            
        Returns:
            bool: 发送是否成功
        """
        if target == "tonghuashun":
            return self.tongdaxin_api.send_stock_code_to_ths(stock_code)
        else:
            print(f"不支持的目标软件: {target}")
            return False
    
    def start_ocr_monitor(self):
        """
        启动OCR监控
        
        Returns:
            bool: 启动是否成功
        """
        return self.tongdaxin_api.start_mouse_hook()
    
    def query_custom_data(self, query, source="wencai", **kwargs):
        """
        自定义查询
        
        Args:
            query (str): 查询语句
            source (str): 数据源，可选值: "wencai"
            **kwargs: 其他参数
            
        Returns:
            查询结果
        """
        if source == "wencai":
            return self.wencai_api.query_data(query, **kwargs)
        else:
            print(f"不支持的自定义查询数据源: {source}")
            return None
    
    def save_data_to_file(self, data, filename=None, file_format="excel", **kwargs):
        """
        保存数据到文件
        
        Args:
            data: 要保存的数据
            filename (str): 文件名
            file_format (str): 文件格式，可选值: "excel", "csv"
            **kwargs: 其他参数
            
        Returns:
            str: 保存的文件路径
        """
        if filename is None:
            filename = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_数据"
        
        if file_format == "excel":
            if isinstance(data, pd.DataFrame):
                return self.wencai_api.save_to_excel(data, f"{filename}.xlsx", **kwargs)
            else:
                # 转换为DataFrame
                df = pd.DataFrame(data)
                return self.wencai_api.save_to_excel(df, f"{filename}.xlsx", **kwargs)
        
        elif file_format == "csv":
            if isinstance(data, pd.DataFrame):
                filepath = f"{filename}.csv"
                data.to_csv(filepath, index=False, encoding='utf-8-sig')
                print(f"数据已保存到: {filepath}")
                return filepath
            else:
                df = pd.DataFrame(data)
                filepath = f"{filename}.csv"
                df.to_csv(filepath, index=False, encoding='utf-8-sig')
                print(f"数据已保存到: {filepath}")
                return filepath
        
        else:
            print(f"不支持的文件格式: {file_format}")
            return None
    
    def get_market_overview(self):
        """
        获取市场概览数据
        
        Returns:
            dict: 市场概览数据
        """
        overview = {}
        
        try:
            # 获取精选股票数据
            selected_stocks = self.kaipaila_api.get_selected_stocks()
            if selected_stocks:
                overview['selected_stocks_count'] = len(selected_stocks)
            
            # 获取涨停股票数据
            limit_up_stocks = self.kaipaila_api.get_limit_up_stocks()
            if limit_up_stocks:
                overview['limit_up_count'] = len(limit_up_stocks)
            
            # 获取市场情绪数据
            sentiment_data = self.kaipaila_api.get_market_sentiment()
            if sentiment_data:
                overview['market_sentiment'] = sentiment_data
            
            # 获取板块强度数据
            sector_data = self.kaipaila_api.get_sector_strength()
            if sector_data:
                overview['sector_strength_count'] = len(sector_data)
            
        except Exception as e:
            print(f"获取市场概览数据时出错: {e}")
        
        return overview
    
    def close_all_connections(self):
        """关闭所有连接"""
        try:
            if hasattr(self.taoguba_api, 'close'):
                self.taoguba_api.close()
            if hasattr(self.tongdaxin_api, 'cleanup_and_exit'):
                self.tongdaxin_api.stop_mouse_hook()
            print("所有连接已关闭")
        except Exception as e:
            print(f"关闭连接时出错: {e}")
    
    def __del__(self):
        """析构函数"""
        self.close_all_connections()


# 使用示例
if __name__ == "__main__":
    # 创建量化数据接口集成实例
    quant_api = QuantDataIntegration()

    try:
        print("=== 量化数据接口集成使用示例 ===")

        # 示例1: 获取市场概览
        print("\n1. 获取市场概览")
        market_overview = quant_api.get_market_overview()
        for key, value in market_overview.items():
            print(f"  {key}: {value}")

        # 示例2: 搜索韭研新闻
        print("\n2. 搜索韭研新闻")
        news_results = quant_api.search_news("涨停", source="jiuyan", max_pages=2)
        print(f"  找到 {len(news_results)} 条相关新闻")
        for news in news_results[:2]:  # 只显示前2条
            print(f"  - {news['title']}")

        # 示例3: 获取开盘啦精选股票
        print("\n3. 获取开盘啦精选股票")
        selected_stocks = quant_api.get_stock_data("selected", source="kaipaila")
        if selected_stocks:
            print(f"  获取到 {len(selected_stocks)} 只精选股票")
            for stock in selected_stocks[:3]:  # 只显示前3只
                print(f"  - 代码: {stock[0]}, 名称: {stock[1]}")

        # 示例4: 同花顺问财查询涨停股票
        print("\n4. 同花顺问财查询涨停股票")
        limit_up_data = quant_api.get_stock_data("limit_up", source="wencai")
        if limit_up_data is not None:
            print(f"  查询到 {len(limit_up_data)} 只涨停股票")

        # 示例5: 获取股票涨停原因
        print("\n5. 获取股票涨停原因")
        reason_data = quant_api.get_stock_reason("300262", source="kaipaila")
        if reason_data:
            print("  成功获取涨停原因数据")

        # 示例6: 自定义查询
        print("\n6. 自定义查询")
        custom_query = "市盈率小于30，市净率小于3，非st"
        custom_data = quant_api.query_custom_data(custom_query, source="wencai")
        if custom_data is not None:
            print(f"  自定义查询获取到 {len(custom_data)} 条数据")

        # 示例7: 保存数据到文件
        if selected_stocks:
            print("\n7. 保存数据到文件")
            excel_file = quant_api.save_data_to_file(
                selected_stocks[:10],  # 只保存前10条数据
                filename="精选股票数据",
                file_format="excel"
            )
            if excel_file:
                print(f"  数据已保存到: {excel_file}")

        # 示例8: 发送股票代码到同花顺（需要同花顺已打开）
        print("\n8. 发送股票代码到同花顺")
        success = quant_api.send_stock_code("000001", target="tonghuashun")
        if success:
            print("  股票代码发送成功")
        else:
            print("  股票代码发送失败（请确保同花顺已打开）")

        print("\n=== 所有示例执行完成 ===")

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {str(e)}")
    finally:
        # 清理资源
        quant_api.close_all_connections()
