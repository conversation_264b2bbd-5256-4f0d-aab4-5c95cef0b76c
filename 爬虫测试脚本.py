"""
异步爬虫测试脚本
测试异步爬虫基础类的各项功能
"""

import asyncio
import json
import time
from pathlib import Path


async def test_basic_spider():
    """测试基础爬虫功能"""
    print("=== 测试基础爬虫功能 ===")
    
    try:
        from aiohttp_base import AsyncSpiderBase
        
        async with AsyncSpiderBase(max_concurrent=3, delay_range=(0.1, 0.5)) as spider:
            
            # 测试1: 单个GET请求
            print("\n1. 测试单个GET请求...")
            response = await spider.get("https://httpbin.org/get")
            if response:
                print(f"   ✓ GET请求成功，响应长度: {len(response)}")
                return True
            else:
                print("   ✗ GET请求失败")
                return False
                
    except ImportError as e:
        print(f"   ✗ 导入模块失败: {e}")
        return False
    except Exception as e:
        print(f"   ✗ 测试异常: {e}")
        return False


async def test_batch_requests():
    """测试批量请求功能"""
    print("\n=== 测试批量请求功能 ===")
    
    try:
        from aiohttp_base import AsyncSpiderBase
        
        async with AsyncSpiderBase(max_concurrent=2) as spider:
            
            # 测试批量GET请求
            print("\n1. 测试批量GET请求...")
            urls = [
                "https://httpbin.org/delay/1",
                "https://httpbin.org/get?test=1",
                "https://httpbin.org/get?test=2"
            ]
            
            start_time = time.time()
            results = await spider.batch_get(urls)
            end_time = time.time()
            
            success_count = sum(1 for r in results if r is not None)
            print(f"   ✓ 批量请求完成，成功: {success_count}/{len(urls)}")
            print(f"   ✓ 耗时: {end_time - start_time:.2f}秒")
            
            return success_count > 0
            
    except Exception as e:
        print(f"   ✗ 批量请求测试异常: {e}")
        return False


async def test_json_requests():
    """测试JSON请求功能"""
    print("\n=== 测试JSON请求功能 ===")
    
    try:
        from aiohttp_base import AsyncSpiderBase
        
        async with AsyncSpiderBase() as spider:
            
            # 测试GET JSON
            print("\n1. 测试GET JSON...")
            json_data = await spider.get_json("https://httpbin.org/json")
            if json_data and isinstance(json_data, dict):
                print(f"   ✓ GET JSON成功，数据键: {list(json_data.keys())}")
            else:
                print("   ✗ GET JSON失败")
                return False
            
            # 测试POST JSON
            print("\n2. 测试POST JSON...")
            post_data = {"test": "data", "timestamp": time.time()}
            response = await spider.post_json("https://httpbin.org/post", json_data=post_data)
            if response and isinstance(response, dict):
                print(f"   ✓ POST JSON成功")
                return True
            else:
                print("   ✗ POST JSON失败")
                return False
                
    except Exception as e:
        print(f"   ✗ JSON请求测试异常: {e}")
        return False


async def test_file_operations():
    """测试文件操作功能"""
    print("\n=== 测试文件操作功能 ===")
    
    try:
        from aiohttp_base import AsyncSpiderBase
        
        async with AsyncSpiderBase() as spider:
            
            # 测试文件下载
            print("\n1. 测试文件下载...")
            download_success = await spider.download_file(
                "https://httpbin.org/image/png",
                "test_downloads/test_image.png"
            )
            
            if download_success:
                print("   ✓ 文件下载成功")
                # 检查文件是否存在
                if Path("test_downloads/test_image.png").exists():
                    print("   ✓ 文件确实已保存")
                else:
                    print("   ✗ 文件未找到")
                    return False
            else:
                print("   ✗ 文件下载失败")
                return False
            
            # 测试数据保存
            print("\n2. 测试数据保存...")
            test_data = {
                "test_time": time.time(),
                "test_data": ["item1", "item2", "item3"],
                "test_dict": {"key": "value"}
            }
            
            await spider.save_to_file(test_data, "test_downloads/test_data.json")
            
            if Path("test_downloads/test_data.json").exists():
                print("   ✓ 数据保存成功")
                return True
            else:
                print("   ✗ 数据保存失败")
                return False
                
    except Exception as e:
        print(f"   ✗ 文件操作测试异常: {e}")
        return False


async def test_callback_crawling():
    """测试回调函数爬取"""
    print("\n=== 测试回调函数爬取 ===")
    
    try:
        from aiohttp_base import AsyncSpiderBase
        
        def process_response(url, response_text):
            """处理响应的回调函数"""
            if response_text:
                try:
                    # 尝试解析JSON
                    data = json.loads(response_text)
                    return {
                        'url': url,
                        'status': 'success',
                        'data_type': 'json',
                        'keys': list(data.keys()) if isinstance(data, dict) else None
                    }
                except:
                    return {
                        'url': url,
                        'status': 'success',
                        'data_type': 'text',
                        'length': len(response_text)
                    }
            return {'url': url, 'status': 'failed'}
        
        async with AsyncSpiderBase() as spider:
            urls = [
                "https://httpbin.org/json",
                "https://httpbin.org/html",
                "https://httpbin.org/get"
            ]
            
            results = await spider.crawl_with_callback(urls, process_response)
            
            success_count = sum(1 for r in results if r and r.get('status') == 'success')
            print(f"   ✓ 回调爬取完成，成功: {success_count}/{len(urls)}")
            
            for result in results:
                if result:
                    print(f"   - {result['url']}: {result['status']} ({result.get('data_type', 'unknown')})")
            
            return success_count > 0
            
    except Exception as e:
        print(f"   ✗ 回调爬取测试异常: {e}")
        return False


async def test_api_spider():
    """测试API爬虫功能"""
    print("\n=== 测试API爬虫功能 ===")
    
    try:
        from API爬虫类 import APISpider
        
        async with APISpider(base_url="https://jsonplaceholder.typicode.com") as api_spider:
            
            # 测试单个API调用
            print("\n1. 测试单个API调用...")
            user_data = await api_spider.api_get("/users/1")
            if user_data and isinstance(user_data, dict):
                print(f"   ✓ API调用成功，用户: {user_data.get('name', 'Unknown')}")
            else:
                print("   ✗ API调用失败")
                return False
            
            # 测试批量API调用
            print("\n2. 测试批量API调用...")
            batch_requests = [
                {"method": "GET", "endpoint": "/users/1"},
                {"method": "GET", "endpoint": "/users/2"},
                {"method": "GET", "endpoint": "/posts/1"}
            ]
            
            batch_results = await api_spider.batch_api_calls(batch_requests)
            success_count = sum(1 for r in batch_results if r is not None)
            print(f"   ✓ 批量API调用完成，成功: {success_count}/{len(batch_requests)}")
            
            return success_count > 0
            
    except ImportError as e:
        print(f"   ✗ 导入API爬虫模块失败: {e}")
        return False
    except Exception as e:
        print(f"   ✗ API爬虫测试异常: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    print("开始异步爬虫功能测试...")
    print("=" * 60)
    
    test_functions = [
        ("基础爬虫功能", test_basic_spider),
        ("批量请求功能", test_batch_requests),
        ("JSON请求功能", test_json_requests),
        ("文件操作功能", test_file_operations),
        ("回调函数爬取", test_callback_crawling),
        ("API爬虫功能", test_api_spider)
    ]
    
    results = []
    
    for test_name, test_func in test_functions:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
                
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    
    passed_count = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总计: {passed_count}/{len(results)} 个测试通过")
    
    if passed_count == len(results):
        print("🎉 所有测试通过！异步爬虫功能正常")
    elif passed_count >= len(results) * 0.7:
        print("✅ 大部分测试通过，基本功能正常")
    else:
        print("⚠️ 多数测试失败，请检查环境和依赖")
    
    print("=" * 60)
    
    # 清理测试文件
    try:
        import shutil
        if Path("test_downloads").exists():
            shutil.rmtree("test_downloads")
            print("测试文件已清理")
    except:
        pass


if __name__ == "__main__":
    # 运行所有测试
    asyncio.run(run_all_tests())
