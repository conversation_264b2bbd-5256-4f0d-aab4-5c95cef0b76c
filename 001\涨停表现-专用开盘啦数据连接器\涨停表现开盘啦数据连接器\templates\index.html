<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>涨停表现</title>
    <style>
        body {
            background: #000;
            color: #fff;
        }
        .container {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: flex-start;
            gap: 0;
        }
        .table-area {
            margin: 0;
        }
        table {
            border-collapse: collapse;
            white-space: nowrap;
            background: #222;
        }
        th, td {
            border: 1px solid #444;
            padding: 8px;
            text-align: center;
            font-size: 12px;
            white-space: nowrap;
            color: #fff;
        }
        th {
            background-color: #333;
        }
        h3, h4, h2 {
            color: #ff3b3b;
        }
        h3 {
            text-align: center;
            font-size: 16px;
            margin: 8px 0 8px 0;
        }
        .divider {
            width: 1px;
            background: #444;
            height: auto;
            margin: 0 4px;
            align-self: stretch;
        }
    </style>
</head>
<body>
    <h2 style="display: flex; align-items: center; gap: 16px;">
        开盘啦 涨停表现 （实时）
        <label style="font-size: 16px; color: #fff; display: flex; align-items: center; gap: 4px; margin-left: 16px;">
            <input type="checkbox" id="realtime-checkbox" style="width: 16px; height: 16px; vertical-align: middle;"> 实时
        </label>
        <a href="https://huop2fuudl.feishu.cn/docx/IGvbdOfbloI2PqxzO0rcqPognnh" target="_blank" style="text-decoration: none; color: #bbb; font-size: 16px; margin-left: 16px; font-weight: normal;">作者更多工具</a>
        <span id="manual-refresh" title="手动刷新" style="cursor:pointer; margin-left: 16px; display: flex; align-items: center;">
            <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 2V5" stroke="#2196f3" stroke-width="2" stroke-linecap="round"/>
                <path d="M11 17V20" stroke="#2196f3" stroke-width="2" stroke-linecap="round"/>
                <path d="M4.22 4.22L6.34 6.34" stroke="#2196f3" stroke-width="2" stroke-linecap="round"/>
                <path d="M15.66 15.66L17.78 17.78" stroke="#2196f3" stroke-width="2" stroke-linecap="round"/>
                <path d="M2 11H5" stroke="#2196f3" stroke-width="2" stroke-linecap="round"/>
                <path d="M17 11H20" stroke="#2196f3" stroke-width="2" stroke-linecap="round"/>
                <path d="M4.22 17.78L6.34 15.66" stroke="#2196f3" stroke-width="2" stroke-linecap="round"/>
                <path d="M15.66 6.34L17.78 4.22" stroke="#2196f3" stroke-width="2" stroke-linecap="round"/>
            </svg>
        </span>
    </h2>
    <a id="tdx-link" href="http://www.treeid/code_000001" style="display:none;"></a>
    <div style="overflow-x: auto; width: 100%;">
      <div class="container">
        <div class="table-area"><h3 id="title-1">一板</h3><table id="table-1"><thead><tr><th>代码</th><th>股票名称</th><th>涨停时间</th><th>涨停原因</th><th>封单</th><th>流通</th></tr></thead><tbody></tbody></table>
        <h4 id="sub-title-1" style="display:none;margin:10px 0 0 0;"></h4><table id="sub-table-1" style="margin-top:0;display:none;"><thead><tr><th>代码</th><th>股票名称</th><th>涨幅</th><th>板块</th><th>流通</th></tr></thead><tbody></tbody></table></div>
        <div class="divider"></div>
        <div class="table-area"><h3 id="title-2">二板</h3><table id="table-2"><thead><tr><th>代码</th><th>股票名称</th><th>涨停时间</th><th>涨停原因</th><th>封单</th><th>流通</th></tr></thead><tbody></tbody></table>
        <h4 id="sub-title-2" style="display:none;margin:10px 0 0 0;"></h4><table id="sub-table-2" style="margin-top:0;display:none;"><thead><tr><th>代码</th><th>股票名称</th><th>涨幅</th><th>板块</th><th>流通</th></tr></thead><tbody></tbody></table></div>
        <div class="divider"></div>
        <div class="table-area"><h3 id="title-3">三板</h3><table id="table-3"><thead><tr><th>代码</th><th>股票名称</th><th>涨停时间</th><th>涨停原因</th><th>封单</th><th>流通</th></tr></thead><tbody></tbody></table>
        <h4 id="sub-title-3" style="display:none;margin:10px 0 0 0;"></h4><table id="sub-table-3" style="margin-top:0;display:none;"><thead><tr><th>代码</th><th>股票名称</th><th>涨幅</th><th>板块</th><th>流通</th></tr></thead><tbody></tbody></table></div>
        <div class="divider"></div>
        <div class="table-area"><h3 id="title-4">四板</h3><table id="table-4"><thead><tr><th>代码</th><th>股票名称</th><th>涨停时间</th><th>涨停原因</th><th>封单</th><th>流通</th></tr></thead><tbody></tbody></table>
        <h4 id="sub-title-4" style="display:none;margin:10px 0 0 0;"></h4><table id="sub-table-4" style="margin-top:0;display:none;"><thead><tr><th>代码</th><th>股票名称</th><th>涨幅</th><th>板块</th><th>流通</th></tr></thead><tbody></tbody></table></div>
        <div class="divider"></div>
        <div class="table-area"><h3 id="title-5">更高</h3><table id="table-5"><thead><tr><th>代码</th><th>股票名称</th><th>涨停时间</th><th>涨停原因</th><th>封单</th><th>流通</th></tr></thead><tbody></tbody></table>
        <h4 id="sub-title-5" style="display:none;margin:10px 0 0 0;"></h4><table id="sub-table-5" style="margin-top:0;display:none;"><thead><tr><th>代码</th><th>股票名称</th><th>涨幅</th><th>板块</th><th>流通</th></tr></thead><tbody></tbody></table></div>
      </div>
    </div>
    <script>
        const pidTypeNames = {1: '一板', 2: '二板', 3: '三板', 4: '四板', 5: '更高'};
        const indices = [0, 1, 4, 5, 6, 13];
        function renderTable(tableId, info, pidType) {
            const tbody = document.querySelector(`#${tableId} tbody`);
            tbody.innerHTML = '';
            info.forEach(row => {
                const tr = document.createElement('tr');
                indices.forEach((idx, i) => {
                    const td = document.createElement('td');
                    if(i === 4) { // 封单列
                        let val = parseFloat(row[idx]);
                        if (!isNaN(val)) {
                            td.textContent = (val / 100000000).toFixed(2);
                        } else {
                            td.textContent = row[idx];
                        }
                    } else if(i === 2) { // 涨停时间列
                        let ts = parseInt(row[idx]);
                        if (!isNaN(ts)) {
                            if(ts.toString().length === 10) ts = ts * 1000;
                            const date = new Date(ts);
                            const pad = n => n.toString().padStart(2, '0');
                            const formatted = `${pad(date.getHours())}:${pad(date.getMinutes())}`;
                            td.textContent = formatted;
                            if(formatted === '09:25') td.style.color = 'red';
                            else if(formatted === '09:30') td.style.color = '#FFD600';
                        } else {
                            td.textContent = row[idx];
                        }
                    } else if(i === 5) { // 流通列
                        let val = parseFloat(row[idx]);
                        if (!isNaN(val)) {
                            td.textContent = (val / 100000000).toFixed(2);
                        } else {
                            td.textContent = row[idx];
                        }
                    } else if(i === 3) { // 涨停原因列，拼接涨停数量
                        const reason = row[idx];
                        const num = row[20]; // 涨停数量
                        td.textContent = reason + (num ? `（${num}）` : '');
                    } else if(i === 1) { // 股票名称，添加点击事件
                        td.textContent = row[idx];
                        td.style.cursor = 'pointer';
                        td.onclick = function() {
                            const code = row[0];
                            const tdxLink = document.getElementById('tdx-link');
                            tdxLink.href = `http://www.treeid/code_${code}`;
                            tdxLink.click();
                        };
                    } else if(i === 0) { // 股票代码，浅灰色
                        td.textContent = row[idx];
                        td.style.color = '#bbb';
                    } else {
                        td.textContent = row[idx];
                    }
                    tr.appendChild(td);
                });
                tbody.appendChild(tr);
            });
            // 设置标题条数
            const title = document.getElementById(`title-${pidType}`);
            if(title) {
                title.textContent = pidTypeNames[pidType] + `（${info.length}）`;
            }
        }
        function fetchAndRender(pidType) {
            const url = `/api/daily_limit/${pidType}`;
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if(data.info && data.info[0]) {
                        renderTable(`table-${pidType}`, data.info[0], pidType);
                    }
                })
                .catch(error => {
                    const tbody = document.querySelector(`#table-${pidType} tbody`);
                    tbody.innerHTML = `<tr><td colspan="6">数据加载失败: ${error}</td></tr>`;
                });
        }
        // 新增：副表格标题映射
        const subTableNames = {
            1: '今日首板破板',
            2: '昨一板未涨停',
            3: '昨二板未涨停',
            4: '昨三板未涨停',
            5: '昨四板未涨停'
        };
        // 新增：渲染副表格
        function renderSubTable(tableId, info, pidType) {
            const tbody = document.querySelector(`#${tableId} tbody`);
            tbody.innerHTML = '';
            info.forEach(row => {
                const tr = document.createElement('tr');
                // 只显示：第1列(0)、第2列(1)、第6列(5)、第7列(6)、第12列(11)
                const indices = [0, 1, 5, 6, 11];
                indices.forEach((idx, i) => {
                    const td = document.createElement('td');
                    if(i === 4) { // 流通列
                        let val = parseFloat(row[idx]);
                        if (!isNaN(val)) {
                            td.textContent = (val / 100000000).toFixed(2);
                        } else {
                            td.textContent = row[idx];
                        }
                    } else if(i === 2) { // 涨幅列
                        let val = parseFloat(row[idx]);
                        td.textContent = row[idx];
                        if (!isNaN(val)) {
                            if(val > 0) td.style.color = 'red';
                            else if(val < 0) td.style.color = 'green';
                        }
                    } else if(i === 1) { // 股票名称，添加点击事件
                        td.textContent = row[idx];
                        td.style.cursor = 'pointer';
                        td.onclick = function() {
                            const code = row[0];
                            const tdxLink = document.getElementById('tdx-link');
                            tdxLink.href = `http://www.treeid/code_${code}`;
                            tdxLink.click();
                        };
                    } else if(i === 0) { // 股票代码，浅灰色
                        td.textContent = row[idx];
                        td.style.color = '#bbb';
                    } else {
                        td.textContent = row[idx];
                    }
                    tr.appendChild(td);
                });
                tbody.appendChild(tr);
            });
            // 显示副表格和标题
            const table = document.getElementById(tableId);
            const title = document.getElementById(`sub-title-${pidType}`);
            if(info.length > 0) {
                table.style.display = '';
                if(title) {
                    title.style.display = '';
                    title.textContent = subTableNames[pidType] + `（${info.length}）`;
                }
            } else {
                table.style.display = 'none';
                if(title) title.style.display = 'none';
            }
        }
        // 新增：请求副表格数据
        function fetchAndRenderSub(pidType) {
            const url = `/api/daily_limit2/${pidType}`;
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if(data.info && data.info[0]) {
                        renderSubTable(`sub-table-${pidType}`, data.info[0], pidType);
                    }
                })
                .catch(error => {
                    const tbody = document.querySelector(`#sub-table-${pidType} tbody`);
                    tbody.innerHTML = `<tr><td colspan="5">数据加载失败: ${error}</td></tr>`;
                    document.getElementById(`sub-table-${pidType}`).style.display = '';
                    const title = document.getElementById(`sub-title-${pidType}`);
                    if(title) {
                        title.style.display = '';
                        title.textContent = subTableNames[pidType] + '（0）';
                    }
                });
        }
        // 刷新函数
        function refreshAll() {
            [1,2,3,4,5].forEach(pidType => {
                fetchAndRender(pidType);
                fetchAndRenderSub(pidType);
            });
        }
        // 实时刷新逻辑
        let realtimeTimer = null;
        const realtimeCheckbox = document.getElementById('realtime-checkbox');
        realtimeCheckbox.addEventListener('change', function() {
            if(this.checked) {
                refreshAll();
                realtimeTimer = setInterval(refreshAll, 5000);
            } else {
                if(realtimeTimer) clearInterval(realtimeTimer);
            }
        });
        // 手动刷新按钮
        document.getElementById('manual-refresh').addEventListener('click', function() {
            refreshAll();
        });
        // 页面初次加载
        refreshAll();
    </script>
</body>
</html>