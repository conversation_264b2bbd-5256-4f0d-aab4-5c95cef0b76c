from flask import Flask, render_template, jsonify, request
import pandas as pd
import os
import numpy as np
import json

app = Flask(__name__)

# 自定义JSON编码器处理NaN值
class NpEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj) if not np.isnan(obj) else None
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, pd.Timestamp):
            return str(obj)
        if pd.isna(obj):
            return None
        return super(NpEncoder, self).default(obj)

app.json_encoder = NpEncoder

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/data')
def get_data():
    try:
        # 读取Excel文件
        df = pd.read_excel('result.xlsx')
        
        # 准备数据
        dates = df['日期'].astype(str).tolist()
        
        # 安全地处理数据和NaN值
        def safe_process(column_name):
            if column_name in df.columns:
                series = df[column_name].copy()
                # 替换无穷值为NaN
                series = series.replace([np.inf, -np.inf], np.nan)
                # 将NaN转换为None
                result = []
                for val in series:
                    if pd.isna(val):
                        result.append(None)
                    else:
                        result.append(val)
                return result
            return []
        
        def safe_percent(numerator, denominator):
            if numerator in df.columns and denominator in df.columns:
                result = []
                for i in range(len(df)):
                    num = df[numerator].iloc[i]
                    den = df[denominator].iloc[i]
                    
                    if pd.isna(num) or pd.isna(den) or den == 0:
                        result.append(None)
                    else:
                        result.append(round((num / den) * 100, 2))
                return result
            return []
        
        # 计算并准备数据
        count_1_percent = safe_percent('值为 1 的条数', '总共的数据条数')
        count_2_percent = safe_percent('值为 2 的条数', '总共的数据条数')
        count_3_percent = safe_percent('值为 3 的条数', '总共的数据条数')
        count_1 = safe_process('值为 1 的条数')
        count_2 = safe_process('值为 2 的条数')
        count_3 = safe_process('值为 3 的条数')
        count_3_plus = safe_process('值为 3 及以上的条数')
        count_3_plus_percent = safe_percent('值为 3 及以上的条数', '总共的数据条数')
        max_values = safe_process('最大值')
        max_values_2 = safe_process('最高2')
        next_day_open_avg = safe_process('次日竞价涨幅平均值')
        next_day_close_avg = safe_process('次日涨幅平均值')
        up_ratio = safe_process('上涨比例')
        bj_up_ratio = safe_process('竞价上涨比例')
        
        # 新增数据
        result1_next_open_avg = safe_process('天板结果为 1 的次日竞价涨幅平均值')
        result1_next_close_avg = safe_process('天板结果为 1 的次日涨幅平均值')
        result2_next_open_avg = safe_process('天板结果为 2 的次日竞价涨幅平均值')
        result2_next_close_avg = safe_process('天板结果为 2 的次日涨幅平均值')
        result3_next_open_avg = safe_process('天板结果大于等于 3 的次日竞价涨幅平均值')
        result3_next_close_avg = safe_process('天板结果大于等于 3 的次日涨幅平均值')
        
        # 计算涨幅差值（仅当收盘涨幅大于竞价涨幅时）
        difference_values = []
        if '次日竞价涨幅平均值' in df.columns and '次日涨幅平均值' in df.columns:
            for i in range(len(df)):
                open_val = df['次日竞价涨幅平均值'].iloc[i] if i < len(df) else None
                close_val = df['次日涨幅平均值'].iloc[i] if i < len(df) else None
                
                if pd.isna(open_val) or pd.isna(close_val):
                    difference_values.append(None)
                elif close_val > open_val:
                    difference_values.append(open_val)  # 记录竞价涨幅作为底部值
                else:
                    difference_values.append(None)  # 不需要显示差异区域
        
        # 返回JSON格式的数据
        return jsonify({
            'dates': dates,
            'count_1_percent': count_1_percent,
            'count_2_percent': count_2_percent,
            'count_3_percent': count_3_percent,
            'count_3_plus_percent': count_3_plus_percent,
            'count_1': count_1,
            'count_2': count_2,
            'count_3': count_3,
            'count_3_plus': count_3_plus,
            'max_values': max_values,
            'max_values_2': max_values_2,
            'next_day_open_avg': next_day_open_avg,
            'next_day_close_avg': next_day_close_avg,
            'difference_values': difference_values,
            'result1_next_open_avg': result1_next_open_avg,
            'result1_next_close_avg': result1_next_close_avg,
            'result2_next_open_avg': result2_next_open_avg,
            'result2_next_close_avg': result2_next_close_avg,
            'result3_next_open_avg': result3_next_open_avg,
            'result3_next_close_avg': result3_next_close_avg,
            'up_ratio': up_ratio,
            'bj_up_ratio': bj_up_ratio
        })
    except Exception as e:
        import traceback
        return jsonify({'error': str(e), 'traceback': traceback.format_exc()})

@app.route('/save_marks', methods=['POST'])
def save_marks():
    try:
        # 获取请求中的标记数据
        marks_data = request.json
        
        # 保存到JSON文件
        with open('marks.json', 'w') as f:
            json.dump(marks_data, f)
        
        return jsonify({"success": True})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/load_marks', methods=['GET'])
def load_marks():
    try:
        # 如果文件不存在，返回空数组
        if not os.path.exists('marks.json'):
            return jsonify({"marks": []})
        
        # 从JSON文件读取标记数据
        with open('marks.json', 'r') as f:
            marks_data = json.load(f)
        
        return jsonify({"marks": marks_data})
    except Exception as e:
        return jsonify({"marks": [], "error": str(e)})

if __name__ == '__main__':
    app.run(host='0.0.0.0',debug=True, port=4001)