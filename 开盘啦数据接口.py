import requests
import json
from datetime import datetime, timedelta


class KaiPanLaDataAPI:
    """
    开盘啦数据接口类
    提供开盘啦APP的各种股票数据接口功能
    """
    
    def __init__(self):
        """初始化开盘啦数据接口"""
        self.base_url = "https://apphq.longhuvip.com/w1/api/index.php"
        self.his_base_url = "https://apphis.longhuvip.com/w1/api/index.php"
        self.headers = {
            'content-type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        self.device_id = "20ad85ca-becb-3bed-b3d4-30032a0f5923"
    
    def get_selected_stocks(self, index=0, zs_type=7, order=1, stock_type=1):
        """
        获取板块排上行数据
        
        Args:
            index (int): 页码索引，第一页为0，第二页为60，第三页为120，第n页为(n-1)*60
            zs_type (int): 指数类型，默认为7
            order (int): 排序方式，默认为1
            stock_type (int): 股票类型，默认为1
            
        Returns:
            list: 股票数据列表，如果失败返回None
        """
        k_lst = [
            "板块ID", "板块名称", "板块强度", "涨幅" , "最高涨幅" , "成交额", "主力净额", "主买", "主卖",
            "未知", "流通值", "未知", "未知", "总市值", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知", "未知",
            ]
        params = {
            'Order': order,
            'a': 'RealRankingInfo',
            'st': 60,
            'apiv': 'w26',
            'Type': stock_type,
            'c': 'ZhiShuRanking',
            'PhoneOSNew': 1,
            'DeviceID': self.device_id,
            'Index': index,
            'ZSType': zs_type
        }
        
        try:
            print(f"正在获取精选股票数据，页码索引: {index}...")
            response = requests.get(self.base_url, params=params, headers=self.headers)
            response.raise_for_status()
            data = response.json()
            
            if 'list' in data:
                print(f"成功获取 {len(data['list'])} 条精选股票数据")
                return data['list']
            else:
                print("响应中未找到股票列表数据")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"获取精选股票数据失败: {e}")
            return None
        except json.JSONDecodeError:
            print("无法解析JSON响应")
            return None
    
    def get_limit_up_reason(self, stock_id):
        """
        获取股票涨停原因
        
        Args:
            stock_id (str): 股票代码
            
        Returns:
            dict: 涨停原因数据，如果失败返回None
        """
        params = {
            'a': 'GetDayZhangTing',
            'st': 25,
            'c': 'HisLimitResumption',
            'PhoneOSNew': 1,
            'DeviceID': 'ffffffff-e91e-5efd-ffff-ffffa460846b',
            'VerSion': '5.12.0.4',
            'Token': 0,
            'Index': 0,
            'apiv': 'w34',
            'StockID': stock_id,
            'UserID': 0
        }
        
        try:
            print(f"正在获取股票 {stock_id} 的涨停原因...")
            response = requests.get(self.his_base_url, params=params, headers=self.headers)
            response.raise_for_status()
            data = response.json()
            print(f"成功获取股票 {stock_id} 的涨停原因")
            return data
            
        except requests.exceptions.RequestException as e:
            print(f"获取股票 {stock_id} 涨停原因失败: {e}")
            return None
        except json.JSONDecodeError:
            print("无法解析JSON响应")
            return None
    
    def get_auction_data(self, r_start="0925", r_end="1500", ratio=6, date="index", 
                        filter_motherboard=0, filter_tib=0, filter_gem=0):
        """
        获取竞价数据
        
        Args:
            r_start (str): 开始时间，默认"0925"
            r_end (str): 结束时间，默认"1500"
            ratio (int): 涨幅比例，默认6
            date (str): 日期，默认"index"
            filter_motherboard (int): 过滤主板，默认0
            filter_tib (int): 过滤科创板，默认0
            filter_gem (int): 过滤创业板，默认0
            
        Returns:
            list: 竞价数据列表，如果失败返回None
        """
        params = {
            'Order': 1,
            'a': 'RealRankingInfo_W8',
            'st': 60,
            'c': 'NewStockRanking',
            'PhoneOSNew': 1,
            'RStart': r_start,
            'DeviceID': self.device_id,
            'VerSion': '5.8.0.2',
            'index': 0,
            'REnd': r_end,
            'apiv': 'w29',
            'Type': 1,
            'FilterMotherboard': filter_motherboard,
            'Filter': 0,
            'Ratio': ratio,
            'FilterTIB': filter_tib,
            'FilterGem': filter_gem,
            'Date': date
        }
        
        try:
            print("正在获取竞价数据...")
            response = requests.get(self.base_url, params=params, headers=self.headers)
            response.raise_for_status()
            data = response.json()
            
            if 'list' in data:
                print(f"成功获取 {len(data['list'])} 条竞价数据")
                return data['list']
            else:
                print("响应中未找到竞价数据列表")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"获取竞价数据失败: {e}")
            return None
        except json.JSONDecodeError:
            print("无法解析JSON响应")
            return None
    
    def get_dragon_tiger_list(self, index=0):
        """
        获取实时龙虎榜数据
        
        Args:
            index (int): 页码索引，默认0
            
        Returns:
            list: 龙虎榜数据列表，如果失败返回None
        """
        params = {
            'Order': 1,
            'a': 'RealRankingInfo',
            'st': 60,
            'apiv': 'w26',
            'Type': 1,
            'c': 'LongHuBang',
            'PhoneOSNew': 1,
            'DeviceID': self.device_id,
            'Index': index
        }
        
        try:
            print(f"正在获取龙虎榜数据，页码索引: {index}...")
            response = requests.get(self.base_url, params=params, headers=self.headers)
            response.raise_for_status()
            data = response.json()
            
            if 'list' in data:
                print(f"成功获取 {len(data['list'])} 条龙虎榜数据")
                return data['list']
            else:
                print("响应中未找到龙虎榜数据列表")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"获取龙虎榜数据失败: {e}")
            return None
        except json.JSONDecodeError:
            print("无法解析JSON响应")
            return None
    
    def get_market_sentiment(self):
        """
        获取市场情绪数据
        
        Returns:
            dict: 市场情绪数据，如果失败返回None
        """
        params = {
            'a': 'GetMarketSentiment',
            'st': 60,
            'c': 'MarketSentiment',
            'PhoneOSNew': 1,
            'DeviceID': self.device_id,
            'apiv': 'w26'
        }
        
        try:
            print("正在获取市场情绪数据...")
            response = requests.get(self.base_url, params=params, headers=self.headers)
            response.raise_for_status()
            data = response.json()
            print("成功获取市场情绪数据")
            return data
            
        except requests.exceptions.RequestException as e:
            print(f"获取市场情绪数据失败: {e}")
            return None
        except json.JSONDecodeError:
            print("无法解析JSON响应")
            return None
    
    def get_stock_detail(self, stock_id):
        """
        获取个股详细数据
        
        Args:
            stock_id (str): 股票代码
            
        Returns:
            dict: 个股详细数据，如果失败返回None
        """
        params = {
            'a': 'GetStockDetail',
            'st': 60,
            'c': 'StockDetail',
            'PhoneOSNew': 1,
            'DeviceID': self.device_id,
            'StockID': stock_id,
            'apiv': 'w26'
        }
        
        try:
            print(f"正在获取股票 {stock_id} 的详细数据...")
            response = requests.get(self.base_url, params=params, headers=self.headers)
            response.raise_for_status()
            data = response.json()
            print(f"成功获取股票 {stock_id} 的详细数据")
            return data
            
        except requests.exceptions.RequestException as e:
            print(f"获取股票 {stock_id} 详细数据失败: {e}")
            return None
        except json.JSONDecodeError:
            print("无法解析JSON响应")
            return None
    
    def get_sector_strength(self, index=0):
        """
        获取板块强度数据
        
        Args:
            index (int): 页码索引，默认0
            
        Returns:
            list: 板块强度数据列表，如果失败返回None
        """
        params = {
            'Order': 1,
            'a': 'RealRankingInfo',
            'st': 60,
            'apiv': 'w26',
            'Type': 1,
            'c': 'SectorStrength',
            'PhoneOSNew': 1,
            'DeviceID': self.device_id,
            'Index': index
        }
        
        try:
            print(f"正在获取板块强度数据，页码索引: {index}...")
            response = requests.get(self.base_url, params=params, headers=self.headers)
            response.raise_for_status()
            data = response.json()
            
            if 'list' in data:
                print(f"成功获取 {len(data['list'])} 条板块强度数据")
                return data['list']
            else:
                print("响应中未找到板块强度数据列表")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"获取板块强度数据失败: {e}")
            return None
        except json.JSONDecodeError:
            print("无法解析JSON响应")
            return None

    def get_big_orders(self, index=0, order_type=1):
        """
        获取大单数据

        Args:
            index (int): 页码索引，默认0
            order_type (int): 大单类型，默认1

        Returns:
            list: 大单数据列表，如果失败返回None
        """
        params = {
            'Order': 1,
            'a': 'RealRankingInfo',
            'st': 60,
            'apiv': 'w26',
            'Type': order_type,
            'c': 'BigOrders',
            'PhoneOSNew': 1,
            'DeviceID': self.device_id,
            'Index': index
        }

        try:
            print(f"正在获取大单数据，页码索引: {index}...")
            response = requests.get(self.base_url, params=params, headers=self.headers)
            response.raise_for_status()
            data = response.json()

            if 'list' in data:
                print(f"成功获取 {len(data['list'])} 条大单数据")
                return data['list']
            else:
                print("响应中未找到大单数据列表")
                return None

        except requests.exceptions.RequestException as e:
            print(f"获取大单数据失败: {e}")
            return None
        except json.JSONDecodeError:
            print("无法解析JSON响应")
            return None

    def get_limit_up_stocks(self, index=0):
        """
        获取涨停股票数据

        Args:
            index (int): 页码索引，默认0

        Returns:
            list: 涨停股票数据列表，如果失败返回None
        """
        params = {
            'Order': 1,
            'a': 'RealRankingInfo',
            'st': 60,
            'apiv': 'w26',
            'Type': 1,
            'c': 'LimitUp',
            'PhoneOSNew': 1,
            'DeviceID': self.device_id,
            'Index': index
        }

        try:
            print(f"正在获取涨停股票数据，页码索引: {index}...")
            response = requests.get(self.base_url, params=params, headers=self.headers)
            response.raise_for_status()
            data = response.json()
            if 'list' in data:
                print(f"成功获取 {len(data['list'])} 条涨停股票数据")
                return data['list']
            else:
                print("响应中未找到涨停股票数据列表")
                return None

        except requests.exceptions.RequestException as e:
            print(f"获取涨停股票数据失败: {e}")
            return None
        except json.JSONDecodeError:
            print("无法解析JSON响应")
            return None

    def get_stock_kline(self, stock_id, period="day", count=100):
        """
        获取股票K线数据

        Args:
            stock_id (str): 股票代码
            period (str): K线周期，默认"day"
            count (int): 获取数量，默认100

        Returns:
            list: K线数据列表，如果失败返回None
        """
        params = {
            'a': 'GetKLine',
            'st': 60,
            'c': 'KLine',
            'PhoneOSNew': 1,
            'DeviceID': self.device_id,
            'StockID': stock_id,
            'Period': period,
            'Count': count,
            'apiv': 'w26'
        }

        try:
            print(f"正在获取股票 {stock_id} 的K线数据...")
            response = requests.get(self.base_url, params=params, headers=self.headers)
            response.raise_for_status()
            data = response.json()

            if 'list' in data:
                print(f"成功获取股票 {stock_id} 的 {len(data['list'])} 条K线数据")
                return data['list']
            else:
                print("响应中未找到K线数据列表")
                return None

        except requests.exceptions.RequestException as e:
            print(f"获取股票 {stock_id} K线数据失败: {e}")
            return None
        except json.JSONDecodeError:
            print("无法解析JSON响应")
            return None

    def get_popular_stocks(self, index=0):
        """
        获取人气榜数据  不可用状态

        Args:
            index (int): 页码索引，默认0

        Returns:
            list: 人气榜数据列表，如果失败返回None
        """
        params = {
            'Order': 1,
            'a': 'RealRankingInfo',
            'st': 60,
            'apiv': 'w26',
            'Type': 1,
            'c': 'PopularStocks',
            'PhoneOSNew': 1,
            'DeviceID': self.device_id,
            'Index': index
        }

        try:
            print(f"正在获取人气榜数据，页码索引: {index}...")
            response = requests.get(self.base_url, params=params, headers=self.headers)
            response.raise_for_status()
            data = response.json()

            if 'list' in data:
                print(f"成功获取 {len(data['list'])} 条人气榜数据")
                return data['list']
            else:
                print("响应中未找到人气榜数据列表")
                return None

        except requests.exceptions.RequestException as e:
            print(f"获取人气榜数据失败: {e}")
            return None
        except json.JSONDecodeError:
            print("无法解析JSON响应")
            return None

    def get_all_pages_data(self, data_type="selected", max_pages=10):
        """
        获取多页数据

        Args:
            data_type (str): 数据类型，可选值: "selected", "auction", "dragon_tiger", "sector", "big_orders", "limit_up", "popular"
            max_pages (int): 最大页数，默认10

        Returns:
            list: 所有页面的数据列表
        """
        all_data = []

        for page in range(max_pages):
            index = page * 60  # 每页60条数据

            if data_type == "selected":
                data = self.get_selected_stocks(index=index)
            elif data_type == "auction":
                data = self.get_auction_data()
            elif data_type == "dragon_tiger":
                data = self.get_dragon_tiger_list(index=index)
            elif data_type == "sector":
                data = self.get_sector_strength(index=index)
            elif data_type == "big_orders":
                data = self.get_big_orders(index=index)
            elif data_type == "limit_up":
                data = self.get_limit_up_stocks(index=index)
            elif data_type == "popular":
                data = self.get_popular_stocks(index=index)
            else:
                print(f"不支持的数据类型: {data_type}")
                break

            if data:
                all_data.extend(data)
                if len(data) < 60:  # 如果返回数据少于60条，说明已经是最后一页
                    break
            else:
                break

        print(f"总共获取 {len(all_data)} 条 {data_type} 数据")
        return all_data


# 使用示例
if __name__ == "__main__":
    # 创建开盘啦数据接口实例
    kpl_api = KaiPanLaDataAPI()

    # 示例1: 获取精选股票数据
    print("=== 获取精选股票数据示例 ===")
    selected_stocks = kpl_api.get_selected_stocks()
    if selected_stocks:
        for stock in selected_stocks[:3]:  # 只显示前3条
            print(stock)
            # print(f"股票代码: {stock[0]}, 股票名称: {stock[1]}, 涨跌幅: {stock[2]}")

    # # 示例2: 获取涨停原因
    # print("\n=== 获取涨停原因示例 ===")
    # limit_reason = kpl_api.get_limit_up_reason("300262")
    # if limit_reason:
    #     print("涨停原因数据获取成功")

    # # 示例3: 获取竞价数据
    # print("\n=== 获取竞价数据示例 ===")
    # auction_data = kpl_api.get_auction_data()
    # if auction_data:
    #     for stock in auction_data[:3]:  # 只显示前3条
    #         print(f"竞价股票: {stock}")

    # # 示例4: 获取多页精选数据
    # print("\n=== 获取多页精选数据示例 ===")
    # all_selected = kpl_api.get_all_pages_data("selected", max_pages=2)
    # print(f"获取到 {len(all_selected)} 条精选股票数据")
