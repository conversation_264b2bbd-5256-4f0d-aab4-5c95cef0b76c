import asyncio
import aiohttp
import json
import time
from typing import List, Dict, Any, Optional, Union
from aiohttp_base import AsyncSpiderBase, RequestConfig


class APISpider(AsyncSpiderBase):
    """
    API专用爬虫类
    继承自AsyncSpiderBase，专门用于API数据爬取
    """
    
    def __init__(self, base_url: str = "", api_key: str = "", **kwargs):
        """
        初始化API爬虫
        
        Args:
            base_url (str): API基础URL
            api_key (str): API密钥
            **kwargs: 其他参数
        """
        super().__init__(**kwargs)
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        
        # 设置API专用的请求头
        self.add_header('Content-Type', 'application/json')
        self.add_header('Accept', 'application/json')
        
        if api_key:
            self.add_header('Authorization', f'Bearer {api_key}')
    
    def build_url(self, endpoint: str) -> str:
        """
        构建完整的API URL
        
        Args:
            endpoint (str): API端点
            
        Returns:
            str: 完整的URL
        """
        if endpoint.startswith(('http://', 'https://')):
            return endpoint
        
        endpoint = endpoint.lstrip('/')
        return f"{self.base_url}/{endpoint}" if self.base_url else endpoint
    
    async def api_get(self, endpoint: str, params: Dict[str, Any] = None, **kwargs) -> Optional[Dict]:
        """
        API GET请求
        
        Args:
            endpoint (str): API端点
            params (dict): 查询参数
            **kwargs: 其他请求参数
            
        Returns:
            dict: API响应数据
        """
        url = self.build_url(endpoint)
        return await self.get_json(url, params=params, **kwargs)
    
    async def api_post(self, endpoint: str, data: Dict[str, Any] = None, **kwargs) -> Optional[Dict]:
        """
        API POST请求
        
        Args:
            endpoint (str): API端点
            data (dict): 请求数据
            **kwargs: 其他请求参数
            
        Returns:
            dict: API响应数据
        """
        url = self.build_url(endpoint)
        return await self.post_json(url, json_data=data, **kwargs)
    
    async def api_put(self, endpoint: str, data: Dict[str, Any] = None, **kwargs) -> Optional[Dict]:
        """
        API PUT请求
        
        Args:
            endpoint (str): API端点
            data (dict): 请求数据
            **kwargs: 其他请求参数
            
        Returns:
            dict: API响应数据
        """
        url = self.build_url(endpoint)
        config = RequestConfig(url=url, method='PUT', json_data=data, **kwargs)
        response = await self._make_request(config)
        
        if response:
            try:
                return await response.json()
            except Exception as e:
                self.logger.error(f"解析JSON失败: {url} - {e}")
        
        return None
    
    async def api_delete(self, endpoint: str, **kwargs) -> Optional[Dict]:
        """
        API DELETE请求
        
        Args:
            endpoint (str): API端点
            **kwargs: 其他请求参数
            
        Returns:
            dict: API响应数据
        """
        url = self.build_url(endpoint)
        config = RequestConfig(url=url, method='DELETE', **kwargs)
        response = await self._make_request(config)
        
        if response:
            try:
                return await response.json()
            except Exception as e:
                self.logger.error(f"解析JSON失败: {url} - {e}")
        
        return None
    
    async def batch_api_calls(self, requests: List[Dict[str, Any]]) -> List[Optional[Dict]]:
        """
        批量API调用
        
        Args:
            requests (List[Dict]): API请求列表，每个字典包含method, endpoint, params/data等
            
        Returns:
            List[Optional[Dict]]: API响应列表
        """
        tasks = []
        
        for req in requests:
            method = req.get('method', 'GET').upper()
            endpoint = req['endpoint']
            
            if method == 'GET':
                task = self.api_get(endpoint, req.get('params'), **req.get('kwargs', {}))
            elif method == 'POST':
                task = self.api_post(endpoint, req.get('data'), **req.get('kwargs', {}))
            elif method == 'PUT':
                task = self.api_put(endpoint, req.get('data'), **req.get('kwargs', {}))
            elif method == 'DELETE':
                task = self.api_delete(endpoint, **req.get('kwargs', {}))
            else:
                self.logger.error(f"不支持的HTTP方法: {method}")
                continue
            
            tasks.append(task)
        
        self.logger.info(f"开始批量API调用，共 {len(tasks)} 个请求")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"批量API调用异常: {requests[i]} - {result}")
                processed_results.append(None)
            else:
                processed_results.append(result)
        
        success_count = sum(1 for r in processed_results if r is not None)
        self.logger.info(f"批量API调用完成，成功: {success_count}/{len(tasks)}")
        
        return processed_results
    
    async def paginated_api_call(self, 
                                 endpoint: str, 
                                 page_param: str = 'page',
                                 size_param: str = 'size',
                                 page_size: int = 100,
                                 max_pages: int = None,
                                 **kwargs) -> List[Dict]:
        """
        分页API调用
        
        Args:
            endpoint (str): API端点
            page_param (str): 页码参数名，默认'page'
            size_param (str): 页面大小参数名，默认'size'
            page_size (int): 每页大小，默认100
            max_pages (int): 最大页数，默认None（无限制）
            **kwargs: 其他请求参数
            
        Returns:
            List[Dict]: 所有页面的数据
        """
        all_data = []
        page = 1
        
        while True:
            if max_pages and page > max_pages:
                break
            
            params = kwargs.get('params', {}).copy()
            params[page_param] = page
            params[size_param] = page_size
            
            self.logger.info(f"获取第 {page} 页数据...")
            response = await self.api_get(endpoint, params=params, **{k: v for k, v in kwargs.items() if k != 'params'})
            
            if not response:
                self.logger.warning(f"第 {page} 页数据获取失败")
                break
            
            # 尝试从响应中提取数据
            page_data = self._extract_page_data(response)
            if not page_data:
                self.logger.info(f"第 {page} 页无数据，停止分页")
                break
            
            all_data.extend(page_data)
            self.logger.info(f"第 {page} 页获取到 {len(page_data)} 条数据")
            
            # 检查是否还有更多页
            if not self._has_more_pages(response, page_data, page_size):
                break
            
            page += 1
        
        self.logger.info(f"分页API调用完成，总共获取 {len(all_data)} 条数据")
        return all_data
    
    def _extract_page_data(self, response: Dict) -> List[Dict]:
        """
        从API响应中提取页面数据
        子类可以重写此方法以适应不同的API响应格式
        
        Args:
            response (Dict): API响应
            
        Returns:
            List[Dict]: 页面数据
        """
        # 常见的数据字段名
        data_fields = ['data', 'items', 'results', 'list', 'records']
        
        for field in data_fields:
            if field in response and isinstance(response[field], list):
                return response[field]
        
        # 如果响应本身就是列表
        if isinstance(response, list):
            return response
        
        return []
    
    def _has_more_pages(self, response: Dict, page_data: List, page_size: int) -> bool:
        """
        判断是否还有更多页
        子类可以重写此方法以适应不同的API响应格式
        
        Args:
            response (Dict): API响应
            page_data (List): 当前页数据
            page_size (int): 页面大小
            
        Returns:
            bool: 是否还有更多页
        """
        # 如果当前页数据少于页面大小，说明没有更多页了
        if len(page_data) < page_size:
            return False
        
        # 检查常见的分页字段
        if 'has_more' in response:
            return response['has_more']
        
        if 'hasNext' in response:
            return response['hasNext']
        
        if 'pagination' in response:
            pagination = response['pagination']
            if 'has_next' in pagination:
                return pagination['has_next']
            if 'total_pages' in pagination and 'current_page' in pagination:
                return pagination['current_page'] < pagination['total_pages']
        
        # 默认认为还有更多页（基于数据量判断）
        return True
    
    async def rate_limited_requests(self, 
                                   requests: List[Dict[str, Any]], 
                                   rate_limit: int = 10,
                                   time_window: int = 60) -> List[Optional[Dict]]:
        """
        限速API请求
        
        Args:
            requests (List[Dict]): API请求列表
            rate_limit (int): 速率限制（每时间窗口的请求数），默认10
            time_window (int): 时间窗口（秒），默认60
            
        Returns:
            List[Optional[Dict]]: API响应列表
        """
        results = []
        request_times = []
        
        for i, req in enumerate(requests):
            current_time = time.time()
            
            # 清理时间窗口外的请求记录
            request_times = [t for t in request_times if current_time - t < time_window]
            
            # 如果达到速率限制，等待
            if len(request_times) >= rate_limit:
                sleep_time = time_window - (current_time - request_times[0])
                if sleep_time > 0:
                    self.logger.info(f"达到速率限制，等待 {sleep_time:.2f} 秒...")
                    await asyncio.sleep(sleep_time)
                    request_times = request_times[1:]  # 移除最早的请求记录
            
            # 执行请求
            method = req.get('method', 'GET').upper()
            endpoint = req['endpoint']
            
            if method == 'GET':
                result = await self.api_get(endpoint, req.get('params'), **req.get('kwargs', {}))
            elif method == 'POST':
                result = await self.api_post(endpoint, req.get('data'), **req.get('kwargs', {}))
            else:
                self.logger.error(f"限速请求不支持的HTTP方法: {method}")
                result = None
            
            results.append(result)
            request_times.append(time.time())
            
            self.logger.info(f"限速请求进度: {i + 1}/{len(requests)}")
        
        return results


# 使用示例
async def api_spider_example():
    """API爬虫使用示例"""
    
    # 创建API爬虫实例
    async with APISpider(
        base_url="https://jsonplaceholder.typicode.com",
        max_concurrent=3
    ) as api_spider:
        
        # 示例1: 单个API调用
        print("=== 单个API调用示例 ===")
        user_data = await api_spider.api_get("/users/1")
        if user_data:
            print(f"用户数据: {user_data.get('name', 'Unknown')}")
        
        # 示例2: 批量API调用
        print("\n=== 批量API调用示例 ===")
        batch_requests = [
            {"method": "GET", "endpoint": "/users/1"},
            {"method": "GET", "endpoint": "/users/2"},
            {"method": "GET", "endpoint": "/posts/1"},
            {"method": "POST", "endpoint": "/posts", "data": {"title": "Test", "body": "Test body", "userId": 1}}
        ]
        
        batch_results = await api_spider.batch_api_calls(batch_requests)
        success_count = sum(1 for r in batch_results if r is not None)
        print(f"批量API调用完成，成功: {success_count}/{len(batch_requests)}")
        
        # 示例3: 分页API调用
        print("\n=== 分页API调用示例 ===")
        # 注意：这个示例API不支持真正的分页，仅作演示
        all_posts = await api_spider.paginated_api_call(
            "/posts",
            page_param="page",
            size_param="limit",
            page_size=10,
            max_pages=2
        )
        print(f"分页获取到 {len(all_posts)} 条帖子数据")


if __name__ == "__main__":
    # 运行API爬虫示例
    asyncio.run(api_spider_example())
